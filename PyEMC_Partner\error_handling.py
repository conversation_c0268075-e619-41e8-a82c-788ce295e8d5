"""
error_handling.py - Error Handling and Logging Module

This module provides centralized error handling and logging functionality
for the PyEMC Partner control system. It ensures consistent error reporting,
logging, and user feedback throughout the application.

Class focused on error handling
"""

import logging
import sys
import traceback
from datetime import datetime
from typing import Optional, Any
from pathlib import Path


class ErrorHandler:
    """
    Centralized error handling and logging class for PyEMC Partner system
    """
    
    def __init__(self, log_file: str = "device_communication.log", log_level: int = logging.INFO):
        """
        Initialize the error handler with logging configuration
        
        Args:
            log_file (str): Path to the log file
            log_level (int): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.log_file = log_file
        self.log_level = log_level
        self.logger = None
        self.setup_logging()
    
    def setup_logging(self):
        """
        Set up logging configuration with both file and console handlers
        """
        try:
            # Create logger
            self.logger = logging.getLogger('PyEMC_Partner')
            self.logger.setLevel(self.log_level)
            
            # Clear any existing handlers
            self.logger.handlers.clear()
            
            # Create formatters
            detailed_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            simple_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            
            # File handler for detailed logging
            try:
                file_handler = logging.FileHandler(self.log_file, mode='a', encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(detailed_formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                print(f"Warning: Could not create file handler for {self.log_file}: {e}")
            
            # Console handler for user feedback
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.WARNING)  # Only show warnings and errors on console
            console_handler.setFormatter(simple_formatter)
            self.logger.addHandler(console_handler)
            
            # Log initialization
            self.logger.info("Error handler initialized successfully")
            
        except Exception as e:
            print(f"Critical error setting up logging: {e}")
            # Fallback to basic logging
            logging.basicConfig(
                level=self.log_level,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            self.logger = logging.getLogger('PyEMC_Partner_Fallback')
    
    def handle_error(self, error_message: str, exception: Optional[Exception] = None, 
                    show_traceback: bool = False) -> None:
        """
        Handle and log errors with consistent formatting
        
        Args:
            error_message (str): Human-readable error description
            exception (Exception, optional): Exception object if available
            show_traceback (bool): Whether to include full traceback in logs
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Format error message
        formatted_message = f"[ERROR] {error_message}"
        
        if exception:
            formatted_message += f" - Exception: {str(exception)}"
        
        # Print to console for immediate user feedback
        print(f"\n{formatted_message}")
        
        # Log detailed error information
        if self.logger:
            self.logger.error(formatted_message)
            
            if exception and show_traceback:
                self.logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Store error for potential later analysis
        self._store_error_info(error_message, exception, timestamp)
    
    def handle_warning(self, warning_message: str) -> None:
        """
        Handle and log warnings
        
        Args:
            warning_message (str): Warning message to log
        """
        formatted_message = f"[WARNING] {warning_message}"
        print(f"\n{formatted_message}")
        
        if self.logger:
            self.logger.warning(warning_message)
    
    def log_info(self, info_message: str) -> None:
        """
        Log informational messages
        
        Args:
            info_message (str): Information message to log
        """
        if self.logger:
            self.logger.info(info_message)
    
    def log_debug(self, debug_message: str) -> None:
        """
        Log debug messages
        
        Args:
            debug_message (str): Debug message to log
        """
        if self.logger:
            self.logger.debug(debug_message)
    
    def log_communication(self, direction: str, message: str) -> None:
        """
        Log communication messages with special formatting
        
        Args:
            direction (str): 'SENT' or 'RECEIVED'
            message (str): Communication message
        """
        formatted_message = f"COMM [{direction}]: {message.strip()}"
        if self.logger:
            self.logger.debug(formatted_message)
    
    def handle_connection_error(self, ip_address: str, port: int, 
                              exception: Optional[Exception] = None) -> None:
        """
        Handle connection-specific errors with helpful suggestions
        
        Args:
            ip_address (str): IP address that failed to connect
            port (int): Port number that failed to connect
            exception (Exception, optional): Connection exception
        """
        error_msg = f"Failed to connect to device at {ip_address}:{port}"
        
        suggestions = [
            "Troubleshooting suggestions:",
            f"1. Verify device is powered on and connected to network",
            f"2. Check IP address {ip_address} is correct",
            f"3. Ensure port {port} is not blocked by firewall",
            f"4. Try pinging the device: ping {ip_address}",
            f"5. Verify device is not already connected to another client"
        ]
        
        self.handle_error(error_msg, exception)
        
        print("\n" + "\n".join(suggestions))
        
        if self.logger:
            for suggestion in suggestions:
                self.logger.info(suggestion)
    
    def handle_parameter_error(self, parameter: str, value: Any, 
                             valid_values: Optional[list] = None) -> None:
        """
        Handle parameter validation errors
        
        Args:
            parameter (str): Parameter name
            value (Any): Invalid value
            valid_values (list, optional): List of valid values
        """
        error_msg = f"Invalid value '{value}' for parameter '{parameter}'"
        
        if valid_values:
            error_msg += f". Valid values: {valid_values}"
        
        self.handle_error(error_msg)
    
    def handle_device_response_error(self, command: str, response: str) -> None:
        """
        Handle errors in device responses
        
        Args:
            command (str): Command that was sent
            response (str): Error response received
        """
        error_msg = f"Device error response for command '{command}': {response}"
        self.handle_error(error_msg)
        
        # Provide common error interpretations
        if "UNKNOWN_COMMAND" in response.upper():
            print("  → The device does not recognize this command")
        elif "BAD_ARGUMENT" in response.upper():
            print("  → The command arguments are invalid or malformed")
        elif "TIMEOUT" in response.upper():
            print("  → The device operation timed out")
        elif "BUSY" in response.upper():
            print("  → The device is busy with another operation")
    
    def _store_error_info(self, error_message: str, exception: Optional[Exception], 
                         timestamp: str) -> None:
        """
        Store error information for later analysis (internal method)
        
        Args:
            error_message (str): Error message
            exception (Exception, optional): Exception object
            timestamp (str): Error timestamp
        """
        # This could be extended to store errors in a database or file
        # for later analysis and reporting
        pass
    
    def create_error_report(self) -> str:
        """
        Create a summary error report from the log file
        
        Returns:
            str: Error report summary
        """
        try:
            if not Path(self.log_file).exists():
                return "No log file found for error report"
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            error_lines = [line for line in lines if 'ERROR' in line]
            warning_lines = [line for line in lines if 'WARNING' in line]
            
            report = f"""
Error Report Summary
====================
Log file: {self.log_file}
Total log entries: {len(lines)}
Error entries: {len(error_lines)}
Warning entries: {len(warning_lines)}

Recent Errors (last 5):
{'-' * 40}
"""
            
            for error_line in error_lines[-5:]:
                report += error_line
            
            return report
            
        except Exception as e:
            return f"Error creating error report: {str(e)}"
    
    def clear_log(self) -> bool:
        """
        Clear the log file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write("")
            self.log_info("Log file cleared")
            return True
        except Exception as e:
            self.handle_error(f"Failed to clear log file: {str(e)}")
            return False
    
    def set_log_level(self, level: int) -> None:
        """
        Change the logging level
        
        Args:
            level (int): New logging level
        """
        self.log_level = level
        if self.logger:
            self.logger.setLevel(level)
            self.log_info(f"Log level changed to {logging.getLevelName(level)}")


# Global error handler instance for convenience
_global_error_handler = None


def get_error_handler() -> ErrorHandler:
    """
    Get the global error handler instance
    
    Returns:
        ErrorHandler: Global error handler instance
    """
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def handle_error(error_message: str, exception: Optional[Exception] = None) -> None:
    """
    Convenience function for handling errors using global error handler
    
    Args:
        error_message (str): Error message
        exception (Exception, optional): Exception object
    """
    get_error_handler().handle_error(error_message, exception)


def log_info(message: str) -> None:
    """
    Convenience function for logging info using global error handler
    
    Args:
        message (str): Info message
    """
    get_error_handler().log_info(message)
