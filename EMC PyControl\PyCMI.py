from PySURGE import IMU3000_device_connection, TestManager
from PyDeviceProperties import Device_Properties


def get_user_input(parameter_name, current_value, valid_values=None):
    """
    Gets user input for a specific paramete
    Args:
        parameter_name: The name of the parameter.
        current_value: The current value of the parameter.
        valid_values: A list of valid values for the parameter (optional).

    Returns:
        The new value entered by the user.
    """
    new_value = input(f"{parameter_name} [{current_value}]: ")
    if new_value:
        if valid_values and new_value not in valid_values:
            print(f"Invalid value for {parameter_name}. Please choose from: {valid_values}")
            return get_user_input(parameter_name, current_value, valid_values)
        return new_value
    else:
        return current_value

def main():
    """
    The main function for controlling the surge generator and running tests.
    """
    
    DEVICE = "************"  # IP address of the server
    PORT = 50500  # Port number for the server

    try:
        # Create an instance of the generator connection
        surge_gen = IMU3000_device_connection(DEVICE, PORT)

        # Ensure connection before querying
        if not surge_gen.connect():
            print("Failed to connect to device. Exiting.")
            return


        # Get initial generator settings
        current_coupling = surge_gen.generator_coupling_path  # Example: Get current coupling
        if current_coupling is None:
            print("Could not get current coupling")
            return
        # ... Get other current settings (level, polarity_change, etc.)



        
        # Define valid values for parameters
        valid_couplings = ["IMP_OUT", "L1_PE", "L1_N", "N_PE"] 

        # Allow user to modify coupling 
        new_coupling = get_user_input("Coupling", current_coupling, valid_couplings)
        if new_coupling != current_coupling:
            surge_gen.generator_coupling_path = new_coupling
            print(f"Coupling changed from {current_coupling} to {new_coupling}")

        # # Check for line-to-line testing
        # line_to_line = input("Have you set up for line-to-line testing? (y/n): ").lower()
        # if line_to_line == 'y':
        #     line_to_line_profile = create_test_profile(
        #         TestProfile.TestZone.ZONE_C,  # Replace with appropriate zone
        #         levels=[500, 1000]
        #     )
        #     line_to_line_profile.run_tests(surge_gen)
        #     print("Line-to-line testing complete.")

        # Set generator state to inactive (example)
        surge_gen.stop_test()  # Assuming this method exists in PySURGE

        # Continue or stop based on user input
        user_input = input("Do you want to run another test? (y/n): ").lower()
        while user_input == 'y':
            main()
            user_input = input("Do you want to run another test? (y/n): ").lower()

        print("Exiting.")

    except Exception as e:
        print(f"An error occurred: {e}")
if __name__ == "__main__":
    main()
#