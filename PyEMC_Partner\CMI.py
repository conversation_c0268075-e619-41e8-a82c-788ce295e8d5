"""
CMI.py - Command and Machine Interface
Direct user interface to interact with EMC Partner devices

This module provides the main command-line interface for controlling
EMC Partner generators. It serves as the primary entry point for users
to interact with the system.
"""

import sys
import os
from typing import Optional
from GeneratorBase import IMU3000_device_connection
from TestManager import Test<PERSON>anager
from CWG_Properties import Device_Properties
from console_ux import ConsoleUX
from error_handling import ErrorHandler


class CMI:
    """
    Command and Machine Interface - Main controller class for EMC Partner devices
    """
    
    def __init__(self, device_ip: str = "************", port: int = 50500):
        """
        Initialize the CMI with device connection parameters
        
        Args:
            device_ip (str): IP address of the EMC Partner device
            port (int): Communication port number
        """
        self.device_ip = device_ip
        self.port = port
        self.device_connection = None
        self.test_manager = None
        self.console_ux = None
        self.error_handler = ErrorHandler()
        
    def initialize_connection(self) -> bool:
        """
        Initialize connection to the EMC Partner device
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.device_connection = IMU3000_device_connection(self.device_ip, self.port)
            
            if not self.device_connection.connect():
                self.error_handler.handle_error("Failed to connect to device")
                return False
                
            # Initialize other components
            self.test_manager = TestManager(self.device_connection)
            self.console_ux = ConsoleUX(self.test_manager, self.error_handler)
            
            self.error_handler.log_info(f"Successfully connected to device at {self.device_ip}:{self.port}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Connection initialization failed: {str(e)}")
            return False
    
    def get_user_input(self, parameter_name: str, current_value: str, valid_values: Optional[list] = None) -> str:
        """
        Gets user input for a specific parameter with validation
        
        Args:
            parameter_name (str): The name of the parameter
            current_value (str): The current value of the parameter
            valid_values (list, optional): List of valid values for validation
            
        Returns:
            str: The new value entered by the user or current value if no input
        """
        prompt = f"{parameter_name} [{current_value}]: "
        new_value = input(prompt).strip()
        
        if new_value:
            if valid_values and new_value not in valid_values:
                print(f"Invalid value for {parameter_name}. Please choose from: {valid_values}")
                return self.get_user_input(parameter_name, current_value, valid_values)
            return new_value
        else:
            return current_value
    
    def display_welcome_message(self):
        """Display welcome message and device information"""
        print("=" * 60)
        print("    PyEMC Partner Control System")
        print("    EMC Generator Command & Control Interface")
        print("=" * 60)
        print(f"Connected to: {self.device_ip}:{self.port}")
        
        if self.device_connection:
            try:
                device_info = self.device_connection.get_device_info()
                if device_info:
                    print(f"Device: {device_info}")
            except Exception as e:
                self.error_handler.handle_error(f"Could not retrieve device info: {str(e)}")
        
        print("=" * 60)
    
    def run_interactive_session(self):
        """
        Run the main interactive session
        """
        if not self.initialize_connection():
            print("Failed to initialize device connection. Exiting.")
            return False
            
        try:
            self.display_welcome_message()
            
            # Start the console user experience
            if self.console_ux:
                self.console_ux.main_menu()
            else:
                self.error_handler.handle_error("Console UX not initialized")
                return False
                
        except KeyboardInterrupt:
            print("\n\nSession interrupted by user.")
            self.cleanup()
        except Exception as e:
            self.error_handler.handle_error(f"Unexpected error in interactive session: {str(e)}")
            self.cleanup()
            return False
        
        return True
    
    def cleanup(self):
        """Clean up resources and disconnect from device"""
        try:
            if self.device_connection:
                self.device_connection.disconnect()
                self.error_handler.log_info("Device disconnected successfully")
        except Exception as e:
            self.error_handler.handle_error(f"Error during cleanup: {str(e)}")


def main():
    """
    Main function for controlling the EMC Partner generator and running tests
    """
    # Default connection parameters - can be overridden via command line or config
    DEVICE_IP = "************"  # Default IP address
    PORT = 50500  # Default port number
    
    # Parse command line arguments if provided
    if len(sys.argv) > 1:
        DEVICE_IP = sys.argv[1]
    if len(sys.argv) > 2:
        try:
            PORT = int(sys.argv[2])
        except ValueError:
            print("Invalid port number provided. Using default port 50500.")
    
    # Create and run CMI instance
    cmi = CMI(DEVICE_IP, PORT)
    
    try:
        success = cmi.run_interactive_session()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)
    finally:
        cmi.cleanup()


if __name__ == "__main__":
    main()
