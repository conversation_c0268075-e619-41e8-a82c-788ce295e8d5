"""
CWG_Properties.py - Device Module Specific Commands for CWG Test Parameters

This module manages various properties related to EMC Partner generators,
providing getters and setters for different test parameters and device configurations.

Device module specific commands for "CWG" test parameters
"""

import re
from typing import Optional, Dict, Any, Union
from error_handling import <PERSON>rrorHandler


class Device_Properties:
    """
    Manages device properties and parameter mappings for EMC Partner generators
    Provides a unified interface for setting and getting device parameters
    """
    
    # Parameter mapping from internal names to device commands
    PARAMETER_MAPPINGS = {
        "peak_voltage": "TEST:CWG:LEVEL:VAL",
        "polarity": "TEST:CWG:POLARITY:VAL",
        "number_of_pulses": "TEST:CWG:PULSE_NB:VAL",
        "repetition_rate": "TEST:CWG:REPETITION:VAL",
        "trigger": "TEST:CWG:TRIGGER:VAL",
        "generator_coupling_path": "TEST:CWG:COUPLING:VAL",
        "coupling_device": "TEST:CWG:COUPLING:DEVICE",
        "impedance": "TEST:CWG:IMPEDANCE:VAL",
        "syncro_degree": "TEST:CWG:SYNCRO:DEGREE:VAL",
        "syncro_mode": "TEST:CWG:SYNCRO:MODE:VAL",
        "multi_coupling_state": "TEST:CWG:MULTI_COUPLING:ENABLE:VAL",
        "change_polarity_after": "TEST:CWG:MULTI_COUPLING:CHANGE_AFTER:VAL",
    }
    
    # Valid values for specific parameters
    VALID_VALUES = {
        "generator_coupling_path": ["IMP_OUT", "L1_PE", "L2_PE", "L3_PE", "N_PE", "L1_N", "L2_N", "L3_N"],
        "coupling_device": ["INTERN", "EXTERN"],
        "impedance": ["50", "75", "AUTO"],
        "polarity": ["POSITIVE", "NEGATIVE", "ALTERNATING"],
        "trigger": ["AUTO", "MANUAL", "EXTERNAL"],
        "syncro_mode": ["AUTO", "MANUAL"]
    }
    
    def __init__(self, device_connection):
        """
        Initialize device properties with default values
        
        Args:
            device_connection: Device connection object for communication
        """
        self.device_connection = device_connection
        self.error_handler = ErrorHandler()
        
        # Initialize default parameter values
        self._generator_coupling_path = "IMP_OUT"
        self._coupling_device = "INTERN"
        self._impedance = "50"
        self._peak_voltage = 1000
        self._polarity = "POSITIVE"
        self._number_of_pulses = 5
        self._repetition_rate = 1
        self._syncro_degree = 0
        self._ramp_vpeak_after = 0
        self._syncro_mode = "AUTO"
        self._trigger = "AUTO"
        self._coupling_change_after = 0
        self._change_polarity_after = "NONE"
        
        # Advanced parameters
        self._multi_coupling_state = False
        self._upper_current_limit_check = False
        self._lower_current_limit_check = False
        self._peak_check_state = False
        self._upper_voltage_limit_check = False
        self._lower_voltage_limit_check = False
        self._ramp_polarity_after = 0
        self._ramp_syncro_after = 0
        self._ramp_syncro_step = 0
        self._ramp_vpeak_step = 0
        self._ramp_vpeak_stop = 0
        self._ramp_polarity_state = False
        self._ramp_syncro_state = False
        self._ramp_vpeak_state = False
        self._coupling_device_frequency = 50
    
    def query(self, command: str) -> Optional[str]:
        """
        Send a query command to the device
        
        Args:
            command (str): Command to send to device
            
        Returns:
            Optional[str]: Device response or None if error
        """
        if hasattr(self.device_connection, 'query'):
            return self.device_connection.query(command)
        else:
            self.error_handler.handle_error("Device connection does not support query method")
            return None
    
    def write(self, command: str) -> bool:
        """
        Send a write command to the device
        
        Args:
            command (str): Command to send to device
            
        Returns:
            bool: True if successful, False otherwise
        """
        if hasattr(self.device_connection, 'write'):
            return self.device_connection.write(command)
        else:
            self.error_handler.handle_error("Device connection does not support write method")
            return False
    
    def validate_parameter_value(self, parameter: str, value: Any) -> bool:
        """
        Validate parameter value against allowed values
        
        Args:
            parameter (str): Parameter name
            value (Any): Value to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if parameter in self.VALID_VALUES:
            valid_values = self.VALID_VALUES[parameter]
            if str(value) not in valid_values:
                self.error_handler.handle_error(
                    f"Invalid value '{value}' for parameter '{parameter}'. "
                    f"Valid values: {valid_values}"
                )
                return False
        return True
    
    def set_parameter(self, parameter: str, value: Any) -> bool:
        """
        Set a device parameter using the parameter mapping
        
        Args:
            parameter (str): Internal parameter name
            value (Any): Value to set
            
        Returns:
            bool: True if successful, False otherwise
        """
        if parameter not in self.PARAMETER_MAPPINGS:
            self.error_handler.handle_error(f"Unknown parameter: {parameter}")
            return False
        
        if not self.validate_parameter_value(parameter, value):
            return False
        
        device_command = self.PARAMETER_MAPPINGS[parameter]
        command = f"{device_command} {value}"
        
        success = self.write(command)
        if success:
            # Update internal value
            setattr(self, f"_{parameter}", value)
            self.error_handler.log_info(f"Parameter set: {parameter} = {value}")
        
        return success
    
    def get_parameter(self, parameter: str) -> Optional[Any]:
        """
        Get a device parameter using the parameter mapping
        
        Args:
            parameter (str): Internal parameter name
            
        Returns:
            Optional[Any]: Parameter value or None if error
        """
        if parameter not in self.PARAMETER_MAPPINGS:
            self.error_handler.handle_error(f"Unknown parameter: {parameter}")
            return None
        
        device_command = self.PARAMETER_MAPPINGS[parameter]
        query_command = f"{device_command}?"
        
        response = self.query(query_command)
        if response:
            # Parse response to extract value
            # Expected format: ":TEST:CWG:PARAMETER:VAL value"
            try:
                value = response.split()[-1].strip()
                return value
            except (IndexError, AttributeError):
                self.error_handler.handle_error(f"Failed to parse response: {response}")
                return None
        
        return None
    
    # Property definitions with getters and setters
    
    @property
    def generator_coupling_path(self) -> str:
        """Get/Set generator coupling path"""
        return self._generator_coupling_path
    
    @generator_coupling_path.setter
    def generator_coupling_path(self, value: str):
        if self.set_parameter("generator_coupling_path", value):
            self._generator_coupling_path = value
    
    @property
    def coupling_device(self) -> str:
        """Get/Set coupling device"""
        return self._coupling_device
    
    @coupling_device.setter
    def coupling_device(self, value: str):
        if self.set_parameter("coupling_device", value):
            self._coupling_device = value
    
    @property
    def impedance(self) -> str:
        """Get/Set output impedance"""
        return self._impedance
    
    @impedance.setter
    def impedance(self, value: str):
        if self.set_parameter("impedance", value):
            self._impedance = value
    
    @property
    def peak_voltage(self) -> int:
        """Get/Set peak voltage level"""
        return self._peak_voltage
    
    @peak_voltage.setter
    def peak_voltage(self, value: int):
        if self.set_parameter("peak_voltage", value):
            self._peak_voltage = value
    
    @property
    def polarity(self) -> str:
        """Get/Set pulse polarity"""
        return self._polarity
    
    @polarity.setter
    def polarity(self, value: str):
        if self.set_parameter("polarity", value):
            self._polarity = value
    
    @property
    def number_of_pulses(self) -> int:
        """Get/Set number of pulses"""
        return self._number_of_pulses
    
    @number_of_pulses.setter
    def number_of_pulses(self, value: int):
        if self.set_parameter("number_of_pulses", value):
            self._number_of_pulses = value
    
    @property
    def repetition_rate(self) -> int:
        """Get/Set repetition rate"""
        return self._repetition_rate
    
    @repetition_rate.setter
    def repetition_rate(self, value: int):
        if self.set_parameter("repetition_rate", value):
            self._repetition_rate = value
    
    @property
    def trigger(self) -> str:
        """Get/Set trigger mode"""
        return self._trigger
    
    @trigger.setter
    def trigger(self, value: str):
        if self.set_parameter("trigger", value):
            self._trigger = value
    
    @property
    def syncro_degree(self) -> int:
        """Get/Set synchronization degree"""
        return self._syncro_degree
    
    @syncro_degree.setter
    def syncro_degree(self, value: int):
        if self.set_parameter("syncro_degree", value):
            self._syncro_degree = value
    
    @property
    def syncro_mode(self) -> str:
        """Get/Set synchronization mode"""
        return self._syncro_mode
    
    @syncro_mode.setter
    def syncro_mode(self, value: str):
        if self.set_parameter("syncro_mode", value):
            self._syncro_mode = value
    
    @property
    def change_polarity_after(self) -> str:
        """Get/Set polarity change setting"""
        return self._change_polarity_after
    
    @change_polarity_after.setter
    def change_polarity_after(self, value: str):
        if self.set_parameter("change_polarity_after", value):
            self._change_polarity_after = value
    
    def get_all_parameters(self) -> Dict[str, Any]:
        """
        Get all current parameter values
        
        Returns:
            Dict[str, Any]: Dictionary of all parameter values
        """
        parameters = {}
        for param_name in self.PARAMETER_MAPPINGS.keys():
            try:
                value = getattr(self, param_name)
                parameters[param_name] = value
            except AttributeError:
                parameters[param_name] = None
        
        return parameters
    
    def set_multiple_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        Set multiple parameters at once
        
        Args:
            parameters (Dict[str, Any]): Dictionary of parameter name-value pairs
            
        Returns:
            bool: True if all parameters set successfully
        """
        success = True
        for param_name, value in parameters.items():
            if not self.set_parameter(param_name, value):
                success = False
        
        return success
    
    def get_idn(self) -> str:
        """Get device identification string for emulator"""
        return "EMC Partner,IMU-3000,SN123456,V1.0.0\n"
    
    def get_lrn(self) -> str:
        """Get learn string with current parameters for emulator"""
        params = self.get_all_parameters()
        lrn_string = ":TEST:CWG:ACTIVE\n"
        
        for param, value in params.items():
            if param in self.PARAMETER_MAPPINGS and value is not None:
                device_cmd = self.PARAMETER_MAPPINGS[param]
                lrn_string += f":{device_cmd} {value}\n"
        
        return lrn_string
    
    def start_test(self) -> str:
        """Return test start response for emulator"""
        return ""
    
    def stop_test(self) -> str:
        """Return test stop response for emulator"""
        return ""
    
    def get_state(self, state: str) -> str:
        """Return device state for emulator"""
        return f"{state}\n"
    
    def set_push_enabled(self) -> str:
        """Return push enabled response for emulator"""
        return ""
    
    def set_push_disabled(self) -> str:
        """Return push disabled response for emulator"""
        return ""
    
    def get_push_state(self, enabled: bool) -> str:
        """Return push state for emulator"""
        state = "ENABLED" if enabled else "DISABLED"
        return f"PUSH {state}\n"
