import socket
import time
#from PySURGE import IMU3000_device_connection, Test
from device_emulator import DeviceEmulator
from PyDeviceProperties import Device_Properties

PORT = 5050
SERVER = "192.168.1.54"
ADDR = (SERVER, PORT)
FORMAT = "utf-8"
DISCONNECT_MESSAGE = "!DISCONNECT"

class MockIMU3000:
    """
    Mock class to simulate IMU3000 device connection. 

    This class mimics the behavior of the actual IMU3000 device 
    for testing purposes without requiring a real device.
    """

    def __init__(self):
        self.test_object = None

    def set_test_values(self, test_data):
        """
        Simulates setting test values on the IMU3000 device.

        This method receives test parameters as a dictionary 
        and stores them in the `test_object` attribute. 
        In a real-world scenario, this would send the parameters 
        to the actual device.
        """
        self.test_object = test_data
        print(f"Received test parameters: {self.test_object.__dict__}")

def handle_client(conn, addr):
    """
    Handles communication with a connected client.

    This function receives the client connection and address, 
    listens for incoming data, processes it, and sends a response.
    """
    print(f"Connected by {addr}")
    mock_connection = MockIMU3000()  # Use the mock device connection

    while True:
        data = conn.recv(1024).decode(FORMAT)
        if not data:
            break
        elif data == DISCONNECT_MESSAGE:
            break

        # Receive and process test parameters from the client
        try:
            test_params = eval(data)  # Assuming data is a dictionary
            mock_connection.set_test_values(test_params)  # Send to mock device
            response = "Test parameters received successfully!"
        except (SyntaxError, NameError):
            response = "Invalid data format. Please send a valid dictionary."

        conn.send(response.encode(FORMAT))

    conn.close()
    print(f"Client {addr} disconnected")

def start_server():
    """
    Starts the server and listens for incoming connections.

    This function creates a socket, binds it to the specified address, 
    listens for incoming connections, and calls the `handle_client` 
    function to handle each connection.
    """
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.bind(ADDR)
    server.listen()
    print(f"Server is listening on {ADDR}")
    while True:
        conn, addr = server.accept()
        handle_client(conn, addr)

def test_generator_parameters(generator):
    # Define a list of test values for each parameter
    test_values = {
        "coupling": ["IMP_OUT", "L1_PE", "N_PE", "L1_N"],
        "level": [100, 200, 300, 400, 500],
        "impedance": ["VAL_AUTO", "VAL_50", "VAL_75"],
        "polarity_change": [1, 2, 3, 4, 5],
        "test_time": [60, 120, 180],
        "trigger": ["AUTO", "MANUAL"],
        "start_polarity": ["POS", "NEG"],
        "pulse_spacing": [30, 60, 90],
        "pulses": [5, 10, 15]
    }

    # Iterate over each parameter and test value
    for param, values in test_values.items():
        for value in values:
            try:
                # Set the parameter to the test value
                setattr(generator, param, value)
                print(f"Successfully set {param} to {value}")
            except Exception as e:
                print(f"Failed to set {param} to {value}: {e}")

def main():
    # Define the IP address and port number for the generator
    DEVICE = "************"
    PORT = 50500

    # Create a connection to the generator
    #surge_gen = IMU3000_device_connection(DEVICE, PORT)
    surge_gen = Device_Properties(DEVICE, PORT)

    # Define the test parameters
    test = Test(
        coupling="IMP_OUT",
        level=500,
        impedance="VAL_AUTO",
        polarity_change=5,
        test_time=120,
        trigger="AUTO",
        start_polarity="POS",
        pulse_spacing=60,
        pulses=10
    )

    # Print the test parameters
    start_server(test)

    user_input = input("Would you like to send the parameters of test set 1? [Y/N]: ").lower()
    
    if user_input == "y":
        try:
            surge_gen.set_test_values(test)
            print(">>The parameters have been sent.")
            query_generator(surge_gen)
            
            user_agree = input("Do you agree with the current settings? [Y/N]: ").lower()
            if user_agree == "n":
                modify_parameters(test)
                print_test_parameters(test)
                user_confirm = input("Do you agree with the updated settings? [Y/N]: ").lower()
                if user_confirm == "y":
                    try:
                        surge_gen.set_test_values(test)
                        print(">>The parameters have been sent.")
                        query_generator(surge_gen)
                    except Exception as e:
                        print(f"An error occurred while sending the parameters: {e}")
                else:
                    print("Settings not confirmed. Exiting.")
            else:
                print("Settings confirmed. Exiting.")
        except Exception as e:
            print(f"An error occurred while sending the parameters: {e}")
    else:
        print("No parameters sent. Exiting.")

    # Test different values for all defined parameters
    test_generator_parameters(surge_gen)

if __name__ == "__main__":
    main()
    start_server()