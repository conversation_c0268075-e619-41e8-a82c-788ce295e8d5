import time
import socket
import traceback
import threading
import multiprocessing

##This class is in the process of being converted! 
class DOW_CGl(object):
	"""This class takes an IP_address as a string, port (ex: 23) as an integer,
	a baudrate as an integer, a timeout as an integer, and a read_timeout. Provided
	the appropriate values are given for the EUT, this should create a 7XX object.
    """
    def	__int__(self, IP_address:str = '0.0.0.0', port:int= 50500, timeout:int= 1, read_timeout:float = 0.3) -> None:
        """
        This method sets up global variables for the Ethernet object.
    
        Parameters: 
        IP address (str): This is the IP address to be used to communicate with the device.
        port (int): This is the port that will be the TCP port used for the ethernet connection (typically 23).
        timeout (float): This is the write timeout for the connection. read timeout (float): This is the read timout for the connection.
    
        returns:
        None: Nothing is returned.
        """
        self.IP_address= IP_address 
        self.port = port 
        self.timeout = timeout 
        self.sock_is_connected= False
        self.generator_responsive = False 
        self.connection_successful= False 
        self.continue_with_recieve = True 
        self.read_timeout= read_timeout 
        self.response = None 
        self.ethernet_device = None 
        self.response_check_array = None 
        self.current_response = None 
        self.command = None
        self.main_command = None 
        self.main_get_response_from_sock = None 
        self.get_response_from_sock = None
        self.close_ethernet_sock_after_communication = None
        self.print_command = None
        self.setup_ethernet_connection()
    
    
    def setup_ethernet_connection(self) -> None:
        """
        If setup is successful sock is connected will be set to true, and true will
        be returned. If setup is unsuccessful sock is connected will be set to false,
        and false will be returned. This method is used internally and it is unlikely
        someone would need to use this method outside the class.
    
        Parameters:
        None (NoneType) No parameters are taken.
    
        returns:
        None: This method does not return anything.
        """
        try:
            self.ethernet_device = socket.socket(socket.AF_INET, socket.SOCK_STREAM) 
            self.ethernet_device.settimeout(5)
            self.ethernet_device.connect((self.IP_address, self.port))
        except:
            print("error setting up ethernet socket")
    
    def buffer_clear(self) -> None:### Could update this algorithm at a later time.
        """
        This method clears the buffer of the ethernet socket.This method is used internally
        and it is unlikely someone would need to use this method outside the class.
    
        Parameters:
        None (NoneType) No parameters are taken.
    
        returns:
        None: This method does not return anything.
        """
        buffer_string = b'' 
        time.sleep(0.1) 
        
        try:
            buffer_string += self.ethernet_device.recv(1028)
            print(buffer_string)
        except IOError:
                # print(f"Error in ethernet buffer clear:\n\n{traceback.print_exc()}\n\n")
            pass
        
    def recieve_response_from_sock(self) -> None:
        """
        This method receives the response from the device and returns the response as a list,
        where each element in the list is a line. The list object is returned to the caller.
        This method does not return the response, but rather modifies the global variable
        "response". This method is used internally and it is unlikely someone would need
        to use this method outside the class. If you would like to recieve a response from
        your generator, see "send- comn1and- to sock()"
        Parameters:
        None (NoneType) No parameters are taken.
    
        returns:
        None: This method does not return anything.
        """
        self.response=""
        try:
            self.response = self.ethernet_device.recv(l024)
        except Exception as e:
            print(f"Exception in recieve response: \n\n {e}")
        return str(self.response)
        pass
    
    def close_ethernet_sock(self) -> None:
        """
        This method closes the ethernet socket. This method is used internally
        and it is unlikely someone would need to use this method outside the class. 126
        Parameters:
        None (NoneType): No parameters are taken. 129
        returns:
        None: This method does not return anything.
        """
        try:
            self.ethernet_device.shutdown(socket.SHUT_RDWR)
            self.ethernet_device.close()
            self.sock_is_connected = False
            self.generator_logged_in = False
        except IOError:
            # print(f"Close ethernet sock exception: \n\n{traceback.print_exc()}\n\n")
            pass
        
    def send_command_to_sock(self, 
                             command: str = "", 
                             close_ethernet_sock_after_communication : bool = False, 
                             get_response_from_sock : bool = True, 
                             print_command : bool = False
                             )->list[bytes]:
    
        self.command = command
        self.close_ethernet_sock_after_communication = close_ethernet_sock_after_communication
        self.get_response_from_sock = get_response_from_sock 
        self.print_command= print_command
    
        time.sleep(0.5)
        self.ethernet_device.send(self.command)
        # return self.recieve response from_sock()
    
    def run_test(self, 
                    level:float = 2500, 
                    frequency:str = "FREQUENCY lM", 
                    test_time:int = 120, 
                    burst_duration:float = 10000.0, 
                    bur_repetition:int = 20, 
                    pulse_spacing:float = 2.5, 
                    polarity:int = 60):#all time is in ms
    
        self.level = level 
        self.burst_duration = burst_duration 
        self.repition_rate = bur_repetition 
        self.pul_spacing = pulse_spacing 
        self.polarity = polarity
        #self.frequency= frequency/1000 
    
        print(f"level: {self.level}") 
        print(f"frequency: {self.frequency}") 
        print(f"test time: {self.t_time}") 
        print(f"burst duration: {self.burst_duration}") 
        print (f"repetition rate: {self.repition_rate}")
        print(f"pulse spacing: {self.pul_spacing}") 
        print(f"change polarity after: {self.polarity}") 
        
        self.send_command_to_sock(b'TEST:ACTIVE DOW_SLOW:\n') 
        self.send_command_to_sock(b'RUN:STOP\n')
        self.send_command_to_sock(f"TEST:DOW_SLOW:LEVEL:VAL {self.level}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:FREQUENCY:VAL {self.frequency}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:TEST_TIME:VAL {self.t_time}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:BURST_DURATION:VAL {self.burst_duration}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:REPETITION:VAL {self.repition_rate}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:PULSE_SPACING:VAL {self.pul_spacing}\n".encode("utf-8"))
        self.send_command_to_sock(f"TEST:DOW_SLOW:RAMP:POLARITY:AFTER:VAL {self.polarity}\n".encode("utf-8"))
    
        self.send_command_to_sock(b'RON:START\n') 
        time.sleep(135)
        print("test ccmplete 1 ")
        self.send_command_to_sock(b'RON:STOP\n')
        #input("Please enter a command (enter 'help' for more info): ") print("Please enter a command (enter 'help' for more info): ")
    
    def run_both_tests(self): 
        levels= [2500, 3000] 
        for lev in levels:
            self.run_test(level = lev)
        pass
        
    def run_standard(self):
        levels= 2500
        self.run_test(level = levels)
        pass
    
    def run_margin(self):
        levels= 3000
        print(f"current.ly testing at {levels}")
        self.run_test(level = levels)
        pass
    
    def run_custom_level(self):
        levels = int (input("Voltage Level to Run; "))
        for lev in levels:
            print(f"currently testing at {lev}")
            self.run_test(level = lev)
            #print(f"currently testing {lev} check values and hit enter..")
            #input( "currently on {freq} at {lev} (burst_dur	{burst dur}) check values and hit enter..")
        print("test comp:ete")
        pass
    
    def user_interface(self): #This is a user interface that makes it easier for non programming people to interact with the script.
        help_string = """
    
        Command:                    Description:
        
        suite                       Starts the test at both standard (2.5 kV) and margin (3.0kV) 
        
        test 1                      Perfomrs the test at standard 2.5 kV
        
        test 2                      Perfomrs the test at margin 3.0 kV
        
        custom test level           test a custom voltage
        
        stop                        stops the test
        
        state                       returns the current state of the generator
        
        idn                         lists device
        
        test command	            send a custom command to the device
    
        """
        
        while 1:
            try:
                user_input = input("Please enter a command (enter 'help' for more info):")
                if user_input.lower() == 'help':
                    print(help_string)
    
                elif user_input.lower() == 'suite': 
                    try:test_thread.join()
                    except: pass
                    test_thread = threading.Thread(target = self.run_both_tests)
                    test_thread.start()
                
                elif user_input.lower()	== 'test l': 
                    try:test_thread.join()
                    except: pass
                    test_thread = threading.Thread(target=self.run_standard)
                    test_thread.start()
    
                elif user_input.lower() == 'test 2': 
                    try:test_thread.join()
                    except: pass
                    test_thread = threading.Thread(target = self.run_margin)
                    test_thread.start()
                    
                elif user_input.lower() == 'idn':
                    self.send_command_to_sock(b"IDN?\n")
                    print(self.recieve_response_from_sock())
                    
                elif user_input.lower() == 'lrn':
                    self.send_command_to_sock(b'*LRN?\n') 
                    print(self.recieve_response_from_sock())
                
                elif user_input.lower() == 'stop':
                    self.send_command_to_sock(b'RUN:STOP\n')
                    print(self.recieve_response_from_sock())
                
                elif user_input.lower() == 'state':
                    self.send_command_to_sock(b'RUN:STATE?\n') 
                    print(self.recieve_response_from_sock())
                    
                elif user_input.lower() == 'custom test level':
                    try:test_thread.join()
                    except: pass
                    test_thread = threading.Thread(target = self.run_custom_level)
                    test_thread.start()
                    
                elif user_input.lower() == 'set end':
                    self.send_command_to_sock(b'SET_END\n')
                    print(self.recieve_response_from_sock())
                    
                elif user_input.lower() =='custom':
                    user_input == input("Please enter a custom command to be sent to the VISA device: ")
                    print(self.send_command_to_sock(user_input))
                except Exception as e:
                    print(f"ERROR!!{e}")




if __name__ == "__main__":
    generator_l = DOW_CGl("l:12.168.4.45")
    generator_l.user_interface()
