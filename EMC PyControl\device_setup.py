import socket
import time
from PySURGE import Test  # Import the Test class

PORT = 5050
SERVER = "localhost"
ADDR = (SERVER, PORT)
FORMAT = "utf-8"
DISCONNECT_MESSAGE = "!DISCONNECT"
CLOSE_COMMAND = "!CLOSE" 

def handle_client(conn, addr):
    """
    Handles communication with the client (e.g., test application).

    This function receives test parameters from the client, 
    creates a Test object, and simulates setting these parameters 
    on the generator.
    """
    print(f"Connected by {addr}")

    while True:
        data = conn.recv(1024).decode(FORMAT)
        if not data:
            break
        elif data == DISCONNECT_MESSAGE:
            break
        elif data == CLOSE_COMMAND:
            response = "Server closing."
            conn.send(response.encode(FORMAT))
            conn.close()
            print(f"Server closed connection with {addr}")
            break

        try:
            test_params = eval(data)  # Assuming data is a dictionary
            test_object = Test(**test_params) 
            print(f"Received test parameters: {test_object.__dict__}")
            # Simulate setting parameters on the generator (replace with actual implementation)
            # generator.set_test_values(test_object) 
            response = "Test parameters set successfully!" 
        except (SyntaxError, NameError):
            response = "Invalid data format. Please send a valid dictionary."

        conn.send(response.encode(FORMAT))

    conn.close()
    print(f"Client {addr} disconnected")


def start():
    """
    Starts the generator server and listens for client connections.
    """
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.bind(ADDR)
    server.listen()
    print(f"Generator server is listening on {ADDR}")
    while True:
        conn, addr = server.accept()
        handle_client(conn, addr)


if __name__ == "__main__":
    start()