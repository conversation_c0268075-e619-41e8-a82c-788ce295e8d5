:TEST:DOW_SLOW:BURST_DURATION:VAL 10000;
:TEST:DOW_SLOW:COUPLING:VAL NO;
:TEST:DOW_SLOW:COUPLING_DEVICE:VAL DIRECT_OUT;
:TEST:DOW_SLOW:FREQUENCY:VAL FREQUENCY_1M;
:TEST:DOW_SLOW:LEVEL:VAL 3000;
:TEST:DOW_SLOW:MULTI_COUPLING:CHANGE_AFTER:VAL 5;
:TEST:DOW_SLOW:MULTI_COUPLING:COUPLINGS:VAL;
:TEST:DOW_SLOW:MULTI_COUPLING:DOUBLE_PE:VAL OFF;
:TEST:DOW_SLOW:MULTI_COUPLING:ENABLE:VAL OFF;
:TEST:DOW_SLOW:POLARITY:VAL POS;
:TEST:DOW_SLOW:PULSE_SPACING:VAL 2.5;
:TEST:DOW_SLOW:RAMP:PEAK:AFTER:VAL 60;
:TEST:DOW_SLOW:RAMP:PEAK:STEP:VAL 1500;
:TEST:DOW_SLOW:RAMP:PEAK:STOP:VAL 2500;
:TEST:DOW_SLOW:RAMP:POLARITY:AFTER:VAL 60;
:TEST:DOW_SLOW:RAMP:SYNC:AFTER:VAL 5;
:TEST:DOW_SLOW:RAMP:SYNC:STEP:VAL 10;
:TEST:DOW_SLOW:RAMP_PEAK:VAL OFF;
:TEST:DOW_SLOW:RAMP_POLARITY:VAL ON;
:TEST:DOW_SLOW:RAMP_SYNC:VAL OFF;
:TEST:DOW_SLOW:REPETITION:VAL 20;
:TEST:DOW_SLOW:SYNCRO:DEGREE:VAL 0;
:TEST:DOW_SLOW:SYNCRO:MODE:VAL OFF;
:TEST:DOW_SLOW:TEST_TIME:VAL 120;
:TEST:DOW_SLOW:TRIGGER:VAL AUTO;
:TEST:DOW_SLOW:INFO "Level","3000 V","Polarity Alternation","enabled","Test time","120 s","Pulse spacing","2.5 ms","Burst duration","10000 ms","Burst repetition","20 s";
:TEST:DOW_SLOW:INFO_EVENTS TEST_STARTED,TEST_FINISHED,IMPULSE_TRIGGERED;
:TEST:DOW_SLOW:INFO_TESTTIME 120;
:TEST:ACTIVE DOW_SLOW;