# PyEMC Software Design Document

## Outline

```
## 1. Introduction
- Purpose
- Scope
- Definitions, Acronyms, and Abbreviations
- References
- Overview

## 2. System Overview
- System Context
- System Functions
- User Characteristics
- Assumptions and Dependencies

## 3. Design Considerations
- Goals and Guidelines
- Development Methods
- Architectural Strategies

## 4. System Architecture
- Architectural Design
- Subsystems and Components
- Interfaces

## 5. Detailed System Design
- Component Design
- Data Design
- Control Design
- User Interface Design

## 6. Policies and Tactics
- Error Handling
- Security
- Performance
- Maintainability

## 7. System Architecture
- Module 1: CMI.py
- Module 2: TestManager.py
- Module 3: GeneratorBase.py
- Module 4: CWG_Properties.py

## 8. Step-by-Step Implementation
- Initialization
- Display Settings
- User Confirmation
- Modify Settings
- Start Test

## 9. Future Enhancements
- Suggestions
```

## 1. Introduction

```text
# Introduction
The PyEMC_Partner software aims to command and control EMC Partner generators through CLI, reducing the need for front panel HMI manipulation. The software is intended for use by technicians and engineers at SEL.
```

*Begin with an overview of the purpose of your software.*

To easily command and control the EMC Partner generators through cli. The following is a list of models:
- [ ] IMU-4000
- [ ] IMU-3000
- [ ] IMU-MG1
- [ ] DOW-3000-S-F
- [ ] DOW-CG1

This software is intended for use at SEL by our:
- Technicians
- Engineers
- etc
## 2. System Overview
```ts
Additional Suggestions
Clarify User Roles: Clearly define the roles and responsibilities of different user types (technicians and engineers) to highlight how the software caters to their specific needs.

Detail System Functions: Provide more detailed descriptions of the system functions, including examples of predefined test profiles and custom command strings.

Highlight Key Features: Emphasize key features such as error handling, logging, and the ability to run parametric tests. This will showcase the software's robustness and flexibility.

Include Assumptions and Dependencies: Clearly state any assumptions and dependencies to set the context for the system's operation and ensure that all necessary conditions are met for the software to function correctly.

Add a System Context Diagram: A visual representation of the system context can help readers understand how different components interact and how the system fits into the larger environment.
```
### 2.1 Purpose

The PyEMC_Partner software aims to provide a command-line interface (CLI) for controlling EMC Partner generators, reducing the need for front panel human-machine interface (HMI) manipulation. This software is intended for use by technicians and engineers at SEL.
### 2.2 Scope

The software allows users to send commands to various EMC Partner generators via CLI, simplifying the interaction with device test capabilities. It supports both predefined test profiles for standard users and custom command strings for advanced users, enhancing usability and efficiency.
### 2.3 User Characteristics

The primary users of this software are:

- **Technicians**: Users who require straightforward, predefined test profiles to perform routine tests.
- **Engineers**: Advanced users who need the flexibility to send custom command strings and perform detailed test configurations.
### 2.4 System Functions

The system provides the following functionalities:

- **Basic User Functions**:
    - Selection and execution of predefined test profiles.
    - Querying and displaying device settings.
    - Viewing current test parameters.
    - Accessing help and usage instructions.
- **Advanced User Functions**:  
    - Sending custom command strings based on onboard test capabilities.
    - Modifying test parameters interactively.
    - Running parametric tests with varying parameters.
    - Executing custom test profiles for detailed configurations.
### 2.5 Assumptions and Dependencies

- **Assumptions**:
    - Users have basic knowledge of CLI operations.
    - The EMC Partner generators are connected to the network and accessible via IP address and port.
- **Dependencies**:    
    - The software relies on the `IMU3000_device_connection` class for device communication.
    - The `SurgeProperties` class manages various properties related to the generator.
    - The `ErrorHandler` class handles logging and error reporting.
    - The `TestProfile` class defines standard test profiles and manages test execution.
### 2.6 System Context

The software operates within the following context:

- **Input**: User commands and test profiles.
- **Output**: Device responses, test results, and logs.
- **Interfaces**: Command-line interface for user interaction and device communication.
### 2.7 Overview Diagram

Include a diagram that illustrates the system architecture, showing the interaction between different components such as `IMU3000_device_connection`, `SurgeProperties`, `ErrorHandler`, and `TestProfile`.
## 3. Design Considerations

```text
# Design Considerations
## Phase 1 Functionality
Send basic test scripts and commands to the generator through CLI.

## Phase 2 Functionality
Integrate with waveform capture software.
```

### Phase 1 Functionality

Send basic test scripts and commands to the generator through CLI.
### Phase 2 Functionality

It will allow for integration into the waveform capture software.
- add more phases as necessary
## 4. Goals and Guidelines

```text
# Goals and Guidelines
- Extend system life
- Ease of use
- Consistent test parameters
```
### 4.1 Architectural Design

The PyEMC_Partner software follows a modular architecture, with separate modules for user interaction, device-specific commands, device interface, test management, error handling, and advanced user functionalities. This design ensures clear separation of concerns, ease of maintenance, and scalability.
### 4.2 Project File Structure

The project is organized into the following file structure:

```
PYSURGE_SURGE/  # Project Folder
|
├── .py_surge-env/  # Virtual environment folder
├── .vscode/  # VSCode configuration folder
├── PyEMC_Partner/  # Main project folder
|   ├── CMI.py  # Direct user interface to interact with the devices
|   ├── CWG_Properties.py  # Device module specific commands for "CWG" test parameters
|   ├── GeneratorBase.py  # Functions that directly affect/control the device (device interface)
|   ├── TestManager.py  # Class and functions to set tests
|   ├── error_handling.py  # Class focused on error handling
|   ├── PowerUser.py  # Class focused on power users
|   └── console_ux.py  # Class and functions related to the console user experience
|
├── Resources/  # Folder for additional resources
|   └── gifs/pictures  # Subfolder for images and gifs
|
├── Script_Archive/  # Archive for old Python scripts
|   └── {old py files}  # Placeholder for old Python files
|
├── .gitignore  # Git ignore file
├── README.md  # Project readme file
├── Requirements.txt  # Project dependencies
└── S_Design.md  # Software design specification
```
### 4.3 Subsystems and Components

The main components of the system are organized into the following modules:

- **CMI.py**: The main script for controlling the generator and running tests. It initializes the `TestProfile` and `ConsoleUX` classes and starts the CLI.
- **CWG_Properties.py**: Manages various properties related to the generator, including getters and setters for different parameters.
- **GeneratorBase.py**: Handles the connection and communication with the generator, including sending commands and receiving responses.
- **TestManager.py**: Manages test parameters and execution, including predefined test profiles and custom test configurations.
- **error_handling.py**: Handles logging and error reporting.
- **PowerUser.py**: Provides advanced functionalities for power users, including interactive parameter setting and custom test execution.
- **console_ux.py**: Manages the console user experience, including menus, user input, and displaying information.
### 4.4 Interfaces

The system interfaces include:

- **Command-Line Interface (CLI)**: For user interaction, allowing users to send commands, select test profiles, and view device settings.
- **Device Communication Interface**: For interacting with EMC Partner generators, sending commands, and receiving responses.
### 4.5 Interaction Diagram

Include a diagram that illustrates the interaction between different components such as `IMU3000_device_connection`, `SurgeProperties`, `ErrorHandler`, and `TestProfile`.
## 5. Detailed System Design

```text
# Development Methods
The development process follows a two-phase approach, focusing on basic functionality first and then integrating advanced features.
```
### 5.1 Component Design

This section provides a detailed breakdown of each component in the system, including their purpose, functionality, and interactions.
#### 5.1.1 CMI.py

- **Purpose**: The main script for controlling the generator and running tests.
- **Functionality**:
    - Initializes the `TestProfile` and `ConsoleUX` classes.
    - Sets the IP address and port for the device connection.
    - Displays a welcome message and main menu.
- **Interactions**: Interacts with the `TestProfile` and `ConsoleUX` classes to manage user input and device communication.
#### 5.1.2 CWG_Properties.py

- **Purpose**: Manages various properties related to the generator.
- **Functionality**:
    - Defines getters and setters for different parameters such as `peak_voltage`, `polarity`, `number_of_pulses`, etc.
    - Handles error reporting and logging.
- **Interactions**: Communicates with the `IMU3000_device_connection` class to send and receive commands.
#### 5.1.3 GeneratorBase.py

- **Purpose**: Handles the connection and communication with the generator.
- **Functionality**:
    - Manages device connection, disconnection, and communication.
    - Sends commands and receives responses from the generator.
    - Provides methods for starting and stopping tests, querying device information, and managing protocols.
- **Interactions**: Interfaces with the generator hardware and other classes such as `SurgeProperties` and `ErrorHandler`.
#### 5.1.4 TestManager.py

- **Purpose**: Manages test parameters and execution.
- **Functionality**:
    - Defines standard test profiles and custom test configurations.
    - Provides methods for running tests, loading profiles, and running parametric tests.
- **Interactions**: Utilizes the `IMU3000_device_connection` and `SurgeProperties` classes to manage test execution and parameter settings.
#### 5.1.5 error_handling.py

- **Purpose**: Handles logging and error reporting.
- **Functionality**:
    - Sets up logging configuration.
    - Provides methods for handling and logging errors.
- **Interactions**: Used by other classes to log errors and handle exceptions.
#### 5.1.6 PowerUser.py

- **Purpose**: Provides advanced functionalities for power users.
- **Functionality**:
    - Allows interactive parameter setting and custom test execution.
    - Provides methods for modifying parameters and running custom tests.
- **Interactions**: Interfaces with the `IMU3000_device_connection` and `SurgeProperties` classes to manage advanced user functionalities.
#### 5.1.7 console_ux.py

- **Purpose**: Manages the console user experience.
- **Functionality**:
    - Displays menus and handles user input.
    - Provides methods for querying test capabilities, displaying test profiles, and managing test execution.
- **Interactions**: Interacts with the `TestProfile` and `IMU3000_device_connection` classes to manage user interactions and device communication.
### 5.2 Data Design

This section describes the data structures and formats used in the system.
#### 5.2.1 Parameter Mappings

- **Description**: Defines mappings for various parameters used in the `SurgeProperties` class.
- **Example**:
```python
PARAMETER_MAPPINGS = {
    "peak_voltage": "LEVEL:VAL",
    "polarity": "POLARITY:VAL",
    "number_of_pulses": "PULSE_NB:VAL",
    "repetition_rate": "REPETITION:VAL",
    "trigger": "TRIGGER:VAL",
    "generator_coupling_path": "COUPLING:VAL",
    "coupling_device": "COUPLING:DEVICE",
    "impedance": "IMPEDANCE:VAL",
    "syncro_degree": "SYNCRO:DEGREE:VAL",
    "syncro_mode": "SYNCRO:MODE:VAL",
    "multi_coupling_state": "MULTI_COUPLING:ENABLE:VAL",
    "change_polarity_after": "MULTI_COUPLING:CHANGE_AFTER:VAL",
}
```

### 5.3 Control Design

This section describes the control flow and logic used in the system.

#### 5.3.1 Main Control Flow

- **Description**: The main control flow is managed by the `CMI.py` script, which initializes the `TestProfile` and `ConsoleUX` classes and starts the CLI.
- **Example**:
```python
def main():
    DEVICE = "************"
    PORT = 50500
    test_profile = TestProfile(DEVICE, PORT)
    console_ux = ConsoleUX(test_profile)
    console_ux.welcome_message()
    console_ux.main_menu()

if __name__ == "__main__":
    main()
```
### 5.4 User Interface Design

This section describes the design of the command-line interface (CLI) and how users interact with the system.

#### 5.4.1 Console User Experience

- **Description**: The `console_ux.py` script manages the console user experience, displaying menus and handling user input.
- **Example**:
```python
class ConsoleUX:
    def __init__(self, test_profile):
        self.test_profile = test_profile

    def welcome_message(self):
        print("Welcome to PyEMC Control!")
        self.device_information()

    def main_menu(self):
        print("Please choose an option:")
        print("1. Query Test Capabilities")
        print("2. Start Test")
        print("3. View Settings")
        print("4. Help")
        print("5. Exit")
        choice = input("> ")
        if choice == "1":
            self.query_test_capabilities()
        elif choice == "2":
            self.test_profile_menu()
        elif choice == "3":
            self.default_test_parameters()
        elif choice == "4":
            self.show_help()
        elif choice == "5":
            self.disconnect_device()
        else:
            print("Invalid option. Please try again.")
            self.main_menu()
```
### 5.5 Error Handling

This section describes the error handling mechanisms used in the system.

#### 5.5.1 ErrorHandler Class

- **Description**: The `error_handling.py` script provides a class for handling and logging errors.
- **Example**:
```python
class ErrorHandler:
    def __init__(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='device_communication.log', filemode='a')
        self.logger = logging.getLogger(__name__)

    def handle_error(self, error_message):
        print(f"[ERROR] >> {error_message}")
        logging.error(error_message)
```
### 5.6 Policies and Tactics

This section describes the policies and tactics used to ensure the system's reliability, security, and maintainability.

#### 5.6.1 Logging

- **Description**: Detailed logging is implemented to track system activities and errors.
- **Example:**
```python
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='device_communication.log', filemode='a')
```
#### 5.6.2 Security

- **Description**: Security measures are implemented to ensure safe and secure communication with the device.
- **Example**: Use of secure communication protocols and validation of user inputs.

#### 5.6.3 Performance

- **Description**: Performance optimization techniques are applied to ensure efficient operation.
- **Example**: Use of efficient data structures and algorithms.

#### 5.6.4 Maintainability

- **Description**: The system is designed for easy maintenance and scalability.
- **Example**: Modular architecture and comprehensive documentation.
## 6. Policies and Tactics
```text
# Architectural Strategies
The software follows a modular architecture, with separate modules for user interaction, device-specific commands, device interface, and test management.
```
### 6.1 Error Handling

Effective error handling is crucial for maintaining the reliability and robustness of the system. The following strategies are implemented to manage errors:

- **Centralized Error Logging**: All errors are logged centrally using the `ErrorHandler` class, which ensures that error messages are consistently formatted and stored in a log file (`device_communication.log`).
- **User-Friendly Error Messages**: Errors are communicated to the user in a clear and concise manner, providing enough information to understand the issue without overwhelming them.
- **Retry Mechanism**: For transient errors, such as network timeouts, the system includes a retry mechanism to attempt the operation again before reporting an error.
- **Example**:
```python
class ErrorHandler:
    def __init__(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='device_communication.log', filemode='a')
        self.logger = logging.getLogger(__name__)

    def handle_error(self, error_message):
        print(f"[ERROR] >> {error_message}")
        logging.error(error_message)
```
### 6.2 Logging

Detailed logging is implemented to track system activities and errors, which aids in debugging and monitoring the system's performance.

- **Activity Logging**: Logs all significant actions performed by the system, including user commands, device responses, and test results.
- **Error Logging**: Logs all errors encountered during the system's operation, including detailed error messages and stack traces.
- **Log Rotation**: Implements log rotation to manage log file sizes and ensure that old logs are archived appropriately.
- **Example**:
```python
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='device_communication.log', filemode='a')
```
### 6.3 Security

Security measures are implemented to ensure safe and secure communication with the device and protect user data.

- **Secure Communication**: Uses secure communication protocols to prevent unauthorized access and data breaches.
- **Input Validation**: Validates all user inputs to prevent injection attacks and ensure that only valid commands are sent to the device.
- **Access Control**: Implements access control mechanisms to restrict access to advanced functionalities based on user roles.
- **Example**:
```python
def validate_input(input_value, valid_values):
    if input_value not in valid_values:
        raise ValueError(f"Invalid input: {input_value}. Must be one of {valid_values}")
```
### 6.4 Performance

Performance optimization techniques are applied to ensure efficient operation and responsiveness of the system.

- **Efficient Data Structures**: Uses efficient data structures and algorithms to minimize memory usage and processing time.
- **Asynchronous Operations**: Implements asynchronous operations for network communication to prevent blocking and improve responsiveness.
- **Resource Management**: Manages system resources effectively, including closing unused connections and freeing up memory.
- **Example**:
```python
async def send_command_async(command):
    await asyncio.sleep(0.1)  # Simulate network delay
    return f"Command {command} sent successfully"
```
### 6.5 Maintainability

The system is designed for easy maintenance and scalability, ensuring that it can be easily updated and extended in the future.

- **Modular Architecture**: Follows a modular architecture with clear separation of concerns, making it easy to update individual components without affecting the entire system.
- **Comprehensive Documentation**: Provides comprehensive documentation for all modules and functionalities, including usage instructions, API references, and design specifications.
- **Code Quality**: Adheres to coding standards and best practices, including code reviews, unit testing, and continuous integration.
- **Example**:
```python
def main():
    DEVICE = "************"
    PORT = 50500
    test_profile = TestProfile(DEVICE, PORT)
    console_ux = ConsoleUX(test_profile)
    console_ux.welcome_message()
    console_ux.main_menu()
if __name__ == "__main__":
    main()
```
### 6.6 Future Enhancements

Suggestions for future enhancements and improvements to ensure the system remains up-to-date and meets evolving user needs.

- **Integration with Waveform Capture Software**: Plan to integrate the system with waveform capture software to provide more comprehensive test capabilities.
- **Advanced Analytics**: Implement advanced analytics and reporting features to provide deeper insights into test results and device performance.
- **User Interface Improvements**: Continuously improve the user interface based on user feedback to enhance usability and user experience.
## 7. System Architecture

```text
# System Architecture
## Components
- **CMI.py**: Main script for controlling the generator and running tests.
- **TestManager.py**: Manages test parameters and execution.
- **GeneratorBase.py**: Handles connection and communication with the generator.
- **CWG_Properties.py**: Manages various properties related to the generator.
```

- Describe the main components/modules and their interactions.
- For each generator type, outline how your software will communicate with it.
### 7.1 CMI.py

- **Purpose**: The main script for controlling the generator and running tests.
- **Functionality**:
    - Initializes the `TestProfile` and `ConsoleUX` classes.
    - Sets the IP address and port for the device connection.
    - Displays a welcome message and main menu.
- **Interactions**: Interacts with the `TestProfile` and `ConsoleUX` classes to manage user input and device communication.
- **Projected Updates**:
    - Enhance the main menu to include new options for advanced user functions.
    - Implement detailed logging for user actions and device responses.
### 7.2 CWG_Properties.py

- **Purpose**: Manages various properties related to the generator.
- **Functionality**:
    - Defines getters and setters for different parameters such as `peak_voltage`, `polarity`, `number_of_pulses`, etc.
    - Handles error reporting and logging.
- **Interactions**: Communicates with the `IMU3000_device_connection` class to send and receive commands.
- **Projected Updates**:
    - Add new properties and methods to support additional test capabilities.
    - Improve error handling and logging mechanisms.
### 7.3 GeneratorBase.py

- **Purpose**: Handles the connection and communication with the generator.
- **Functionality**:
    - Manages device connection, disconnection, and communication.
    - Sends commands and receives responses from the generator.
    - Provides methods for starting and stopping tests, querying device information, and managing protocols.
- **Interactions**: Interfaces with the generator hardware and other classes such as `SurgeProperties` and `ErrorHandler`.
- **Projected Updates**:
    - Complete the full implementation of lines 321 - 544.
    - Implement detailed logging for all communication with the device.
    - Enhance the retry mechanism for transient errors.
### 7.4 TestManager.py

- **Purpose**: Manages test parameters and execution.
- **Functionality**:
    - Defines standard test profiles and custom test configurations.
    - Provides methods for running tests, loading profiles, and running parametric tests.
- **Interactions**: Utilizes the `IMU3000_device_connection` and `SurgeProperties` classes to manage test execution and parameter settings.
- **Projected Updates**:
    - Add new test profiles and configurations based on user feedback.
    - Implement advanced analytics and reporting features for test results.
### 7.5 error_handling.py

- **Purpose**: Handles logging and error reporting.
- **Functionality**:
    - Sets up logging configuration.
    - Provides methods for handling and logging errors.
- **Interactions**: Used by other classes to log errors and handle exceptions.
- **Projected Updates**:
    - Implement log rotation to manage log file sizes.
    - Enhance error messages to include more detailed information for debugging.
### 7.6 PowerUser.py

- **Purpose**: Provides advanced functionalities for power users.
- **Functionality**:
    - Allows interactive parameter setting and custom test execution.
    - Provides methods for modifying parameters and running custom tests.
- **Interactions**: Interfaces with the `IMU3000_device_connection` and `SurgeProperties` classes to manage advanced user functionalities.
- **Projected Updates**:
    - Add new custom command strings and test configurations for power users.
    - Implement a more intuitive user interface for advanced functions.
### 7.7 console_ux.py

- **Purpose**: Manages the console user experience.
- **Functionality**:
    - Displays menus and handles user input.
    - Provides methods for querying test capabilities, displaying test profiles, and managing test execution.
- **Interactions**: Interacts with the `TestProfile` and `IMU3000_device_connection` classes to manage user interactions and device communication.
- **Projected Updates**:
    - Enhance the user interface to provide a more intuitive and user-friendly experience.
    - Add new menu options for advanced user functions and detailed logging.
### 7.8 TestManager.py

- **Purpose**: Manages test profiles and execution.
- **Functionality**:
    - Defines standard test profiles and custom test configurations.
    - Provides methods for running tests, loading profiles, and running parametric tests.
- **Interactions**: Utilizes the `IMU3000_device_connection` and `SurgeProperties` classes to manage test execution and parameter settings.
- **Projected Updates**:
    - Add new test profiles and configurations based on user feedback.
    - Implement advanced analytics and reporting features for test results.
## 8. Step-by-Step Implementation

```text
# Policies and Tactics
- Error handling
- Logging
- Security measures
```
### 8.1 Initialization

The initialization process sets up the necessary parameters and establishes the connection to the device.
#### 8.1.1 Create Instances

Create instances of the necessary classes to associate device command parameters and manage test profiles.
```python
DEVICE_IP = "************"
DEVICE_PORT = 50500

# Create an instance of TestProfile
test_profile = TestProfile(DEVICE_IP, DEVICE_PORT)

# Pass the test_profile instance to ConsoleUX
console_ux = ConsoleUX(test_profile)
```
#### 8.1.2 Retrieve Device Settings

Use the `learn` function to retrieve and parse device settings.
```python
test_parameters = test_profile.device_connection.get_current_test_parameters()
if test_parameters:
    cleaned_parameters = test_parameters.replace(":*", "")
    parsed_parameters = console_ux.parse_test_parameters(cleaned_parameters)
    print("Current Test Parameters:")
    for key, value in parsed_parameters.items():
        print(f"{key}: {value}")
else:
    print("Failed to retrieve test parameters.")
```
### 8.2 Display Current Settings

Print the current settings retrieved from the device to provide the user with an overview of the device configuration.
```python
console_ux.device_information()
```
### 8.3 User Confirmation

Prompt the user for confirmation to start the test with the current settings or modify them.
```python
print("Do you want to start the test with the current settings or modify them?")
print("1. Start Test")
print("2. Modify Settings")
choice = input("> ")
if choice == "1":
    console_ux.start_test()
elif choice == "2":
    console_ux.modify_parameters()
else:
    print("Invalid option. Please try again.")
```
### 8.4 Modify Settings if Needed

Allow the user to select and update specific parameters interactively.
```python
console_ux.modify_paramters()
```
### 8.5 Start the Test

Start the test with the confirmed settings and wait for completion.
```python
console_ux.start_test()
```
### 8.6 Error Handling

Implement descriptive error messages and logging to ensure that any issues are communicated clearly and logged for debugging.
```python
try:
    # Code for starting the test
except Exception as e:
    console_ux.handle_error(f"Error starting the test: {e}")
```
### 8.7 Detailed Steps for Each Module

Provide detailed implementation steps for each module, including initialization, user interactions, and test execution.

#### 8.7.1 IMU3000_device_connection

The `IMU3000_device_connection` class handles the connection and communication with the generator.

##### ******* Initialize Parameters and Retrieve Device Settings

Create instances to associate device command parameters and use the `learn` function to retrieve and parse device settings.
```python
device_connection = IMU3000_device_connection(DEVICE_IP, DEVICE_PORT)
device_connection.connect_to_device()
test_parameters = device_connection.get_current_test_parameters()
```
##### ******* Display Current Settings

Print the current settings retrieved from the device.
```python
device_connection.device_information()
```
##### ******* Prompt User for Confirmation

Ask the user if they want to start the test with the current settings or modify them.
```python
print("Do you want to start the test with the current settings or modify them?")
choice = input("> ")
```
##### ******* Modify Settings if Needed

Allow the user to select and update specific parameters.
```python
device_connection.modify_parameters()
```
##### ******* Start the Test

Start the test with the confirmed settings.
```python
device_connection.start_test()
```

##### ******* Error Handling

Improve error messages to be more descriptive and consider using logging instead of print statements for better traceability.
```python
try:
    device_connection.start_test()
except Exception as e:
    device_connection.handle_error(f"Error starting the test: {e}")
```

#### 8.7.2 Example Function: `set_test_values`

Provide an example function for setting test values interactively.
```python
def set_test_values(self, interactive=False) -> Surge_Test:
    properties = ["peak_voltage", "coupling", "impedance", ...]  # List of property names
    for prop in properties:
        current_value = getattr(self, prop, None)
        new_value = input(f"{prop.replace('_', ' ').title()} [{current_value}]: ") or current_value
        setattr(self, prop, new_value)
```
### 8.8 Future Enhancements

Suggest future enhancements and improvements to ensure the system remains up-to-date and meets evolving user needs.

- **Integration with Waveform Capture Software**: Plan to integrate the system with waveform capture software to provide more comprehensive test capabilities.
- **Advanced Analytics**: Implement advanced analytics and reporting features to provide deeper insights into test results and device performance.
- **User Interface Improvements**: Continuously improve the user interface based on user feedback to enhance usability and user experience.
## 9. Detailed System Design

```text
# Detailed System Design
## CMI.py
- **Purpose**: Main script for controlling the generator and running tests.
- **Functionality**: Establishes device connection, updates parameters, allows user modifications, and manages test execution.

## TestManager.py
- **Purpose**: Manages test parameters and execution.
- **Functionality**: Handles user input, updates parameters from the device, modifies parameters, and executes tests.

## GeneratorBase.py
- **Purpose**: Handles connection and communication with the generator.
- **Functionality**: Manages device connection, communication, test management, error reporting, and protocol management.

## CWG_Properties.py
- **Purpose**: Manages various properties related to the generator.
- **Functionality**: Defines getters and setters for generator properties, including coupling path, impedance, peak voltage, and advanced options.
```

- This is where you get into specifics. Break down your software into smaller pieces:
- Classification: Categorize components (e.g., user interface, communication layer, control logic).
- Definition: Describe each component in detail.
- Responsibilities: What does each component handle?
- Constraints: Any limitations or rules for each component.
- Composition: How the components fit together.
#### CMI.py 

- **Function Purpose**: The `main` function is the entry point for controlling the generator and running tests.
- **Device Connection**: `IMU3000_device_connection` is used to establish a connection with the device.
- **Parameter Management**: `TestManager` is used to manage and update test parameters.
- **User Interaction**: The script interacts with the user to confirm if they want to modify parameters.
This file contains the main script for controlling the generator and running tests. Here's a breakdown of its functionality:

- **Imports**: It imports necessary modules and classes (`IMU3000_device_connection`, `TestManager`, and `SurgeProperties`).
- **Main Function**: The `main` function is the entry point of the script. It:
    - Establishes a connection to the device using `IMU3000_device_connection`.
    - Updates parameters from the device using `TestManager`.
    - Interacts with the user to confirm if they want to modify parameters.
    - Optionally starts a test based on user input.
    - Disconnects from the device.
- **Error Handling**: It includes error handling to catch and print any exceptions that occur during execution.

#### CWG_Properties.py

**Purpose**
This file defines the `SurgeProperties` class, which manages various properties related to the generator. 

**Functionality:**
- **Initialization**: The `__init__` method initializes the class.
- **Properties**: It defines several properties with getter and setter methods to manage different parameters of the generator, such as:
    - `generator_coupling_path`
    - `coupling_device`
    - `impedance`
    - `peak_voltage`
    - `polarity`
    - `number_of_pulses`
    - `repetition_rate`
    - `syncro_degree`
    - `ramp_vpeak_after`
    - `syncro_mode`
    - `trigger`
    - `coupling_change_after`
    - `change_polarity_after`
- **Advanced Options**: It includes advanced settings that can be dealt with later, such as `multi_coupling_coupling_double_pe`, `multi_coupling_state`, `upper_current_limit_check`, `lower_current_limit_check`, `peak_check_state`, `upper_voltage_limit_check`, `lower_voltage_limit_check`, `ramp_polarity_after`, `ramp_syncro_after`, `ramp_syncro_step`, `ramp_vpeak_step`, `ramp_vpeak_stop`, `ramp_polarity_state`, `ramp_syncro_state`, `ramp_vpeak_state`, and `coupling_device_frequency`.

#### GeneratorBase.py
This file defines the `IMU3000_device_connection` class, which handles the connection and communication with the generator. Here's a breakdown of its functionality:

- **Initialization**: The `__init__` method initializes the class with the IP address and port of the device.
- **Connection Management**: It includes methods to:
    - `connect`: Opens the connection to the device and verifies it.
    - `disconnect`: Closes the connection to the device.
    - `__enter__` and `__exit__`: Context manager methods for connecting and disconnecting.
    - `ping`: Verifies the connection by sending a ping command.
- **Communication**: It includes methods to:
    - `learn`: Sends the 'LRN?' command to retrieve current settings.
    - `query`: Sends a command and reads the response.
    - `write`: Sends a command to the device.
    - `read`: Reads the response from the device.
- **Test Management**: It includes methods to:
    - `start_test`: Starts a test on the device.
    - `stop_test`: Stops a test on the device.
    - `wait_until_test_complete`: Waits until the test is complete.
- **Error Reporting**: It includes methods to report errors and get device information.
- **Protocol Management**: It includes methods to manage protocol settings, such as setting and getting company name, operator name, EUT description, serial number, comments, and enabling/disabling CSV and HTML protocols.
#### TestManager.py
This file defines the `TestManager` class, which manages the test parameters and execution. Here's a breakdown of its functionality:

- **Initialization**: The `__init__` method initializes the class with default parameters.
- **User Interaction**: It includes methods to:
    - `get_user_input`: Gets user input for a specific parameter.
    - `set_test_values`: Allows the user to set test parameters interactively.
    - `modify_parameters`: Allows the user to select and modify parameters until satisfied.
    - `print_parameters`: Prints the parameters in the desired format.
- **Parameter Management**: It includes methods to:
    - `update_parameters_from_device`: Updates the test parameters from the device using the learn function.
    - `extract_parameters`: Extracts relevant test parameters from the device response.
    - `parse_response`: Parses the cleaned response string to extract key-value pairs.
- **Test Execution**: It includes methods to:
    - `surge_ac_zone_A_0_deg`: Executes a test at 500V and 0 degrees.
    - `run_test`: Runs a test with specified parameters.
    - `IMP_OUT_500V`, `IMP_OUT_1000V`, `IMP_OUT_2000V`, `IMP_OUT_4000V`, `IMP_OUT_4800V`, `IMP_OUT_2400V`: Define test profiles with different voltage levels.
    - `EUT_POWER_500V`, `EUT_POWER_1000V`, `EUT_POWER_2000V`, `EUT_POWER_4000V`, `EUT_POWER_2400V`, `EUT_POWER_4800V`: Define EUT-POWER test profiles with different voltage levels.
    - `run_coupling_tests`: Runs coupling tests with specified voltage levels and phase angles.
    - `ZoneA_EUT_AC_PS_Test_Suite`, `ZoneB_EUT_AC_PS_Test_Suite`: Define test suites for different zones.
    - `EUT_POWER_DC_CUSTOM`, `EUT_POWER_AC_CUSTOM`, `IMP_OUT_CUSTOM`: Custom functions for power users to run tests with custom parameters.

## 10. Step-by-Step Implementation

```text
1. Initialize Parameters and Retrieve Device Settings:
    - Create instances to associate device command parameters.
    - Use the `learn` function to retrieve and parse device settings.

2. Display Current Settings:
    - Print the current settings retrieved from the device.

3. Prompt User for Confirmation:
    - Ask the user if they want to start the test with the current settings or modify them.

4. Modify Settings if Needed:
    - Allow the user to select and update specific parameters.

5. Start the Test:
    - Start the test with the confirmed settings.
```

### 10.1 IMU3000_device_connection

The `IMU3000_device_connection` class handles:

- **Initialization**: Setting up IP address, port, and other attributes.
- **Connection Management**: Methods like `connect`, `disconnect`, `__enter__`, `__exit__`.
- **Communication**: Methods like `query`, `write`, `read`.
- **Device Commands**: Methods like `learn`, `start_test`, `stop_test`, `wait_until_test_complete`.
- **Utility**: Methods like `list_serial_devices`.

#### 10.1.1 Detailed Steps

##### ******** Initialize Parameters and Retrieve Device Settings

- **Create Instances**: Create instances to associate device command parameters.
- **Retrieve Settings**: Use the `learn` function to retrieve and parse device settings.

##### ******** Display Current Settings

- **Print Settings**: Print the current settings retrieved from the device.

##### ******** Prompt User for Confirmation

- **User Confirmation**: Ask the user if they want to start the test with the current settings or modify them.

##### ******** Modify Settings if Needed

- **User Selection**: Allow the user to select and update specific parameters.

##### ******** Start the Test

- **Start Test**: Start the test with the confirmed settings.

##### ******** Error Handling

- **Descriptive Error Messages**: Improve error messages to be more descriptive.
- **Logging**: Consider using logging instead of print statements for better traceability.

### 10.1.2 Example Function: `set_test_values`

```
def set_test_values(self, interactive=False) -> Surge_Test:
    """
    Allows the user to set test parameters interactively.
    """
    properties = ["peak_voltage", "coupling", "impedance", ...]  # List of property names
    for prop in properties:
        current_value = getattr(self, prop, None)
        new_value = input(f"{prop.replace('_', ' ').title()} [{current_value}]: ") or current_value
        setattr(self, prop, new_value)
```

- **Properties List**: The function defines a list of property names (e.g., "peak_voltage", "coupling", "impedance", etc.).
- **Iteration**: It iterates over this list of property names.
- **Get Current Value**: For each property name, it uses `getattr()` to get the current value of the corresponding attribute from the `Surge_Test` object.
- **User Input**: It prompts the user to enter a new value for the property. If the user provides a new value, it updates the attribute using `setattr()`.
- **Set New Value**: If the user does not provide a new value, it retains the current value.

#### 11. Suggestions for Future Implementation

- **Future Enhancements**: Suggest future enhancements and improvements.
- **Example**:
    
```text
    # Suggestions for Future Implementation
    - Simplify user input handling.
    - Enhance error handling.
    - Modularize code.
    - Add documentation.
```

#### 12. Explain Functionality

- **Module Functionality**: Provide detailed explanations of each module's functionality.
- **Example**:
```text
### CMI.py
- **Function Purpose**: The `main` function is the entry point for controlling the generator and running tests.
- **Device Connection**: `IMU3000_device_connection` is used to establish a connection with the device.
- **Parameter Management**: `TestManager` is used to manage and update test parameters.
- **User Interaction**: The script interacts with the user to confirm if they want to modify parameters.

### CWG_Properties.py
- **Initialization**: The `__init__` method initializes the class.
- **Properties**: It defines several properties with getter and setter methods to manage different parameters of the generator, such as:
  - `generator_coupling_path`
  - `coupling_device`
  - `impedance`
  - `peak_voltage`
  - `polarity`
  - `number_of_pulses`
  - `repetition_rate`
  - `syncro_degree`
  - `ramp_vpeak_after`
  - `syncro_mode`
  - `trigger`
  - `change_polarity_after`
  - `ramp_polarity_after`

### GeneratorBase.py
- **Initialization**: The `__init__` method initializes the class with the IP address and port of the device.
- **Connection Management**: It includes methods to:
  - `connect`: Opens the connection to the device and verifies it.
  - `disconnect`: Closes the connection to the device.
  - `__enter__` and `__exit__`: Context manager methods for connecting and disconnecting.
- **Communication**: It includes methods to:
  - `learn`: Sends the 'LRN?' command to retrieve current settings.
  - `query`: Sends a command and reads the response.
  - `write`: Sends a command to the device.
  - `read`: Reads the response from the device.
- **Test Management**: It includes methods to:
  - `start_test`: Starts a test on the device.
  - `stop_test`: Stops a test on the device.
  - `wait_until_test_complete`: Waits until the test is complete.
- **Error Reporting**: It includes methods to report errors and get device information.
- **Protocol Management**: It includes methods to manage protocol settings, such as setting and getting company name, operator name, EUT description, serial number, comments, and enabling/disabling CSV and HTML protocols.

### TestManager.py
- **Initialization**: The `__init__` method initializes the class with default parameters.
- **User Interaction**: It includes methods to:
  - `get_user_input`: Gets user input for a specific parameter.
  - `set_test_values`: Allows the user to set test parameters interactively.
  - `modify_parameters`: Allows the user to select and modify parameters until satisfied.
  - `print_parameters`: Prints the parameters in the desired format.
- **Parameter Management**: It includes methods to:
  - `update_parameters_from_device`: Updates the test parameters from the device using the learn function.
  - `extract_parameters`: Extracts relevant test parameters from the device response.
  - `parse_response`: Parses the cleaned response string to extract key-value pairs.
- **Test Execution**: It includes methods to:
  - `surge_ac_zone_A_0_deg`: Executes a test at 500V and 0 degrees.
  - `run_test`: Runs a test with specified parameters.
  - `IMP_OUT_500V`, `IMP_OUT_1000V`, `IMP_OUT_2000V`, `IMP_OUT_4000V`, `IMP_OUT_4800V`, `IMP_OUT_2400V`: Define test profiles with different voltage levels.
  - `EUT_POWER_500V`, `EUT_POWER_1000V`, `EUT_POWER_2000V`, `EUT_POWER_4000V`, `EUT_POWER_2400V`, `EUT_POWER_4800V`: Define EUT-POWER test profiles with different voltage levels.
  - `run_coupling_tests`: Runs coupling tests with specified voltage levels and phase angles.
  - `ZoneA_EUT_AC_PS_Test_Suite`, `ZoneB_EUT_AC_PS_Test_Suite`: Define test suites for different zones.
  - `EUT_POWER_DC_CUSTOM`, `EUT_POWER_AC_CUSTOM`, `IMP_OUT_CUSTOM`: Custom functions for power users to run tests with custom parameters.
```
