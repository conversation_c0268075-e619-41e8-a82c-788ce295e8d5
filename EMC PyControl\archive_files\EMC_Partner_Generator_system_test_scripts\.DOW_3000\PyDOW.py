import socket, time

#
# Data class for creating a test sequence, why this exists is explained in the High-Level interface section below.
# The purpose of this class is to hold values, and sometimes do basic data manipulation.
# The benefit of this is that values are given names are packaged nicely, and avoids confusing data structuring. 
# ie. To get the amplitude object you aren't accessing element 0 of list "Args", you're accessing the "amplitude" property of "TestObject".
#
class Test:

    # Constructors of data classes like this define how flexible operations can get.
    # If you only want the user to be able to modify the amplitude of the waveform, only provide an amplitude argument.
    # For every argument you provide you must be able to properly handle edge cases & value restrictions.
    # ie. In this example I only allow the user to modify amplitude and frequency, nothing else should change throughout a normal test so
    # I don't allow them to change the other values w/o having them go out of their way to do so.
    def __init__(self, amplitude_V: int, freq_Hz: int, ):
        self.Amplitude_V = amplitude_V  
        self.Frequency_Hz = freq_Hz
        self.TestTime = 120000
        self.BurstRepetition = 300
        self.BurstDuration = 50
        self.PulseSpace = 0.2
        #if Frequency_Hz == 
        #add freq / rise time
#
# Interface class for the DOW generator.
# The main purpose of this guy would be to handle communications and 
#
class DOW3000:

    def __init__(self, ipStr: str, port: int = 50500):
        self.IpAddress = ipStr
        self.Port = port
        self.Device = None
        
        self.OpenPort()
        if (not self.ping()):
            print("Error: Could not ping device.")
            return

    # region High-Level Interface
    # This is ideally the only thing the user should ever be interacting with.
    # It should completely focus on making it stupid easy and error free. 
    # Issue with this is that it takes a lot of development time to get right.

    # Generally, my preferred way to go about this is to take in data classes that define everything needed to perform a task.
    # That way the task and the generator process can be separated so the user only has to worry about creating a task
    # and not interacting with the generator.
    # This region should take advantage of functions/properties in the Mid-Level/Low-Level interfaces.
    
    def RunTest(self, testToRun: Test):
        self.Frequency = testToRun.Frequency_Hz # This is interacting with the property defined in the mid level & is actually sending a command to the device.
        # So on and so on for everything else you need to define

        self.RunTest()
        self.WaitUntilTestComplete()
        self.StopTest()


    # endregion

    # region Mid-Level Interface
    # This is the what the user will be interacting with if they want to more directly control how things are happening or run
    # a sequence that isn't supported.
    # Consequently, you're less 
    # There are a few ways to go about it, they all work it just depends on how you want to interact with the device.
    
    # Function Interfacing
    # Pretty common, flexible, easy to understand.
    # When possible use the property method in place of getters & setters. getters/setters are common in Python but are kinda dumb.
    # If everything is functions & everything has getters/setters using the module becomes very cluttered.

    def SetFrequency(self, frequency_Hz: int):
        # idk might want to add a limit check
        self.Write("Whatever the set frequency command is idk get off my back")

    def GetFrequency(self):
        resp = self.Read("Whatever the set frequency command is idk get off my back")
        returnValue = "Put cool data processing here to make it a format you can easily use"
        return returnValue

    # Properties
    # If you want to be cool you can use properties.
    # Here you can add focused value checking & setting the values on the 
    # This will have a really high-quality feel when interacting with the interface.

    @property
    def Frequency(self):
        resp = self.Query("hub gub get frequency ::??:://")
        returnValue = "Put cool data processing here to make it a format you can easily use"
        return returnValue
    
    @Frequency.setter
    def Frequency(self, frequency_Hz: int):
        # idk might want to add a limit check
        self.Write("Whatever the set frequency command is idk get off my back")

    @property
    def State(self):
        resp = self.Query("Get status & stuff command")
        # idk what the state command returns but you can parse out all the info and store it into a data class
        return resp
    # State is typically a read-only thing so all I have to do is not provide a setter.

    # In the context of equipment interfaces functions are best used for command pass-through.
    def StartTest(self):
        self.Write('RUN:START\n')

    def StopTest(self):
        self.Write('RUN:STOP\n')

    def WaitUntilTestComplete(self):
        # This would be expecting state to be a data class that gives that info
        # idk if that would work or not but this would be much better than sleeping.
        # In general if you are using a sleep command for sequence control you are doing something wrong.
        while (self.State.TestRunning):
            time.sleep(0.1)

    # endregion

    # region Low-Level Interface
    # User shouldn't ever need to interact with these functions but are useful to have.
    # Your intention here should generally be to convert raw data into usable data types.
    # If the user is using these you are completely free from fault of the module failing.
    # If they're using these then they should know what they're doing.

    def OpenPort(self):
        self.Device = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.Device.settimeout(5)
        self.Device.connect((self.IpAddress, self.Port))

    def ClosePort(self):
        self.Device = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.Device.settimeout(5)
        self.Device.connect((self.IpAddress, self.Port))

    def Write(self, msg: str):
        self.Device.send(msg.encode("utf-8"))

    def Read(self) -> str|None:
        return self.Device.recv(1024).decode("utf-8")

    def Query(self, msg: str) -> str|None:
        self.Write(msg)
        return self.Read()

    def ping(self):
        return self.Query(b"*IDN?\n") == "" # idk what the response looks like but verify it and return a bool
    
    # endregion