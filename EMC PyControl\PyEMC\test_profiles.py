from enum import Enum

class TestProfile:
    """
    Represents a test profile with configurable parameters.
    """

    class TestZone(Enum):
        ZONE_A = 1
        ZONE_B = 2
        ZONE_C = 3
        # Add more zones as needed

        def __init__(self, zone: TestZone, levels: list, coupling: str,
                     coupling_device: str, polarity_change: int, pulses: int, repetition: int):
            self.zone = zone
            self.levels = levels
            self.coupling = coupling
            self.coupling_device = coupling_device
            self.polarity_change = polarity_change
            self.pulses = pulses
            self.repetition = repetition

        def run_tests(self, generator):
            """
            Executes the tests defined in the profile.

            Args:
                generator: An instance of the IMU3000_device_connection class or a compatible interface.
            """

            for level in self.levels:
                print(f"Currently testing at {level} V")
                generator.level = level
                # Apply other parameters to the generator (e.g., coupling, polarity_change)
                generator.coupling = self.coupling
                generator.eut_coupling = self.coupling_device  # Assuming this sets coupling device
                generator.alternating_polarity = self.polarity_change
                generator.number_of_pulses = self.pulses
                generator.repetition_rate = self.repetition

                # Execute the test
                generator.StartTest()
                generator.WaitUntilTestComplete()
                
        def set_test_values(self, test: Generator_Build):
        """
        Sets test parameters on the generator based on the provided Test object.
        """
        try:
            self.coupling = test.coupling
            self.level = test.level
            self.impedance = test.impedance
            self.polarity_change = test.polarity_change
            self.test_time = test.test_time
            self.trigger = test.trigger
            self.start_polarity = test.start_polarity
            self.pulse_spacing = test.pulse_spacing
            self.pulses = test.pulses
            self.gen_coupling = self.get_coupling_index(self.coupling)
        except Exception as e:
            raise InvalidParameterError(f"Error setting test values: {e}")

        def get_coupling_index(self, coupling: str) -> Optional[int]:
            """
            Maps coupling string to its corresponding index.

            Args:
                coupling: The coupling mode string.

            Returns:
                The corresponding index, or None if the coupling is not recognized.
            """
            coupling_map = {
                Generator_Couplings.IMP_OUT: 0,
                Generator_Couplings.L1_PE: 1,
                Generator_Couplings.L1_N: 2,
                Generator_Couplings.N_PE: 3,
                # ... (Add other mappings) ...
            }
            return coupling_map.get(coupling, None)
            
    class Generator_Couplings:
        """
        Enum for generator coupling modes.
        """
        IMP_OUT = "IMP_OUT"
        L1_PE = "L1_PE"
        N_PE = "N_PE"
        L1_N = "L1_N"


    class Test_Profile_Names:
        """
        Enum for predefined test profile names.
        """
        ZONE_A = "Zone A"
        ZONE_B = "Zone B"
        CUSTOM = "custom"

def create_test_profile(zone: TestProfile.TestZone, levels: list, **kwargs):
    """
    Creates a TestProfile object with the specified zone and levels.

    Args:
        zone: The test zone (e.g., TestProfile.TestZone.ZONE_B).
        levels: A list of voltage levels for the test.

    Keyword arguments (kwargs) can be used to specify additional parameters like:
        coupling: The coupling mode (e.g., "IMP_OUT").
        coupling_device: The coupling device (e.g., "INTERN").
        polarity_change: The polarity change setting.
        pulses: The number of pulses.
        repetition: The pulse repetition rate.

    Returns:
        A TestProfile object.
    """

    default_params = {
        "coupling": "IMP_OUT",
        "coupling_device": "INTERN",
        "polarity_change": 5,
        "pulses": 10,
        "repetition": 60,
    }

    # Combine default parameters with kwargs
    params = {**default_params, **kwargs}

    return TestProfile(zone, levels, **params)