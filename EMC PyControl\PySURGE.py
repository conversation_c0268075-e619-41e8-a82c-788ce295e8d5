import re
import time
import socket
import serial.tools.list_ports
from typing import Optional
from PyDeviceProperties import Device_Properties

class Surge_Test:

    def __init__(
        self,
        coupling_device: Optional[str] = "INTERN",
        level: Optional[int] = None,
        polarity: Optional[str] = None,
        change_polarity_after: Optional[int] = None,
        impedance: Optional[str] = None,
        trigger: Optional[str] = "AUTO",
        repetition: Optional[int] = None,
        number_of_pulses: Optional[int] = None,
        generator_coupling_path: Optional[str] = None,
    ):
        """
        Initializes the SurgeTest class with default parameters.
        """
        self.parameters = {
            "coupling_device": coupling_device,
            "peak_voltage": level,
            "start_polarity": polarity,
            "change_polarity_after": change_polarity_after,
            "impedance": impedance,
            "trigger": trigger,
            "pulse_spacing": repetition,
            "pulses": number_of_pulses,
            "generator_coupling_path": generator_coupling_path
        }


class TestManager:
    def __init__(self, surge_gen : Surge_Test, device_connection):
        self.surge_gen = surge_gen
        self.device_connection = device_connection

    # This function allows you to set test parameters interactively.
    def set_test_values(self, interactive : bool = False) -> None:
        """
        Allows the user to set test parameters interactively.
        """
        if interactive:
            properties = [
                "peak_voltage",
                "start_polarity",
                "change_polarity_after",
                "pulse_spacing",
                "pulses",
                "generator_coupling_path"
            ]
            try:
                for prop in properties:
                    current_value = self.surge_gen.parameters.get(prop)
                    new_value = input(f"{prop.replace('_', ' ').title()} [{current_value}]: ") or current_value
                    self.surge_gen.parameters[prop] = new_value
                    print(f"{prop.replace('_', ' ').title()} changed from {current_value} to {new_value}")
            except KeyboardInterrupt:
                print("\nInput process interrupted. Exiting...")

    def update_parameters_from_device(self):
        """
        Updates the test parameters from the device using the learn function.
        """
        parameters = self.device_connection.learn
        if parameters:
            for key, value in parameters.items():
                if key in self.surge_gen.parameters:
                    self.surge_gen.parameters[key] = value
            print("Parameters updated from device.")
        else:
            print("Failed to update parameters from device.")

class IMU3000_device_connection:
    def __init__(self, ipStr: str, port: int):
        self.IpAddress = ipStr
        self.Port = port
        self.Device = None
        self.LogCommunications = True
        self.device_properties = Device_Properties(self)

    # Opens the connection to the device.
    def connect(self) -> bool:
        """
        Opens the connection to the device and verifies the connection.
        """
        try:
            self.Device = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.Device.settimeout(5)
            self.Device.connect((self.IpAddress, self.Port))
            print("Connected to device.")
            # Verify the connection by sending a simple command
        except socket.error as e:
            print(f"Connection error: {e}")
            self.Device = None
            return False
        
    def query(self, msg: str) -> Optional[str]:
        try:
            self.write(msg)
            return self.read()
        except MemoryError:
            print(f"Error: Not enough memory.")

    def write(self, msg: str):
        if not self.Device:
            print("Error: No active connection to the device.\n")
            return
        if not msg.endswith("\n"):
            msg += "\n"
        self.Device.send(msg.encode("utf-8"))
        print(f"[SENDING] >> {msg}")

    def read(self) -> Optional[str]:
        if not self.Device:
            print("Error: No active connection to the device.")
            return None
        response = self.Device.recv(1300).decode("utf-8")
        print(f"[RECEIVING] << {response}")
        return response

    def extract_value(self, response: str) -> str:
        """
        Extracts the value from a response string.
        """
        return response.split()[-1]

    def extract_parameters(self, response: str) -> dict:
        """
        Extracts key-value pairs from the device response.
        """
        parameters = {}
        for line in response.split(";"):
            if not line.strip():
                continue  # Skip empty lines
            parts = line.split(":")
            if len(parts) >= 3:
                key = parts[1].strip().lower().replace(" ", "_")
                value = self.extract_value(parts[2].strip())
                parameters[key] = value
            else:
                print(f"Unexpected response format: {line}")
        return parameters

    def disconnect(self):
        """Closes the connection to the device."""
        if self.Device:
            self.Device.close()  # ensures any resources associated with the sockets use are released
            self.Device = None # ensures there are no active connections to the device or socket
            print("Disconnected from device.")

    def __enter__(self):
        if not self.connect():
            raise ConnectionError("Failed to connect to device.")
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.disconnect()


    def ping(self):
        """
        Verifies the device's response and returns a boolean indicating success.
        """       
        try:
            response = self.query("*IDN?")
            if response is not None and "UNKNOWN_COMMAND" not in response:
                return True
            else:
                if response is None:
                    print("Error: No response from the device.")
                else:
                    print(f"Error: The command '*IDN?' is not recognized by the device. Response: {response}")
                self.disconnect()
                return False
        except socket.timeout as e:
            print(f"Error: The device did not respond in time {e}.")
            return False
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return False        

    def __del__(self):
        print("The port is now closing.")
        self.disconnect()

        # Function to list connected serial devices
    @staticmethod
    def list_serial_devices():
        ports = serial.tools.list_ports.comports()
        devices = [port.device for port in ports]
        return devices
    
    def start_test(self):
        self.write("RUN:START\n")

    def stop_test(self):
        self.write("RUN:STOP\n")

    def wait_until_test_complete(self):
        # This would be expecting state to be a data class that gives that info
        # idk if that would work or not but this would be much better than sleeping.
        # In general if you are using a sleep command for sequence control you are doing something wrong.
        while self.State.TestRunning:
            time.sleep(0.1)

    @property
    def generator_coupling_path(self) -> Optional[str]:
        return self.device_properties.generator_coupling_path

    @generator_coupling_path.setter
    def generator_coupling_path(self, value: str) -> None:
        self.device_properties.generator_coupling_path = value





    # Allow the user to modify each parameter
    def modify_parameter(self):
        """
        Allows the user to modify the generator_coupling_path parameter.
        """
        try:
            current_coupling = self.generator_coupling_path
            if current_coupling is None:
                print("Could not get current coupling")
                return
            new_coupling = input(f"Coupling [{current_coupling}]: ") or current_coupling
            self.generator_coupling_path = new_coupling
            print(f"Coupling changed to: {new_coupling}")
        except Exception as e:
            print(f"An error occurred: {e}")

    @property
    def state(self):
        resp = self.query("RUN:STATE?")
        return resp

    @property
    def push(self):
        resp = self.query("PUSH?")
        return resp

    @property
    def learn(self):
        """
        Sends the 'LRN?' command to the device to retrieve the current settings.
        Ensures the full response is received and handles any errors.
        """
        try:
            response = self.query("*LRN?")
            print(f"Initial response: {response}")  # Log the initial response
            if "UNKNOWN_COMMAND" in response:
                print("Error: The command '*LRN?' is not recognized by the device.")
                return ""
        except socket.timeout:
            print("Error retrieving parameters: timed out")
            return ""
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return ""
            
    def test_simple_command(self):
        """
        Sends a simple command to the device to verify communication.
        """
        try:
            response = self.query("*IDN?")
            print(f"Response to *IDN?: {response}")
            if "UNKNOWN_COMMAND" in response:
                print("Error: The command '*IDN?' is not recognized by the device.")
                return False
            return True
        except Exception as e:
            print(f"An error occurred: {e}")
            return False

    def test_lrn_command(self):
        """
        Sends the 'LRN?' command to the device to verify if it's being accepted.
        """
        try:
            response = self.query("*LRN?")
            print(f"Response to *LRN?: {response}")
            if "UNKNOWN_COMMAND" in response:
                print("Error: The command '*LRN?' is not recognized by the device.")
                return False
            return True
        except Exception as e:
            print(f"An error occurred: {e}")
            return False