import re
from typing import Optional

class Device_Properties:
    def __init__(self, device_connection):
        self._generator_coupling_path = "IMP_OUT"
        self._coupling_device = "INTERN"
        self._impedance = "50"
        self._peak_voltage = 1000
        self._polarity = "POSITIVE"
        self._number_of_pulses = 5
        self._repetition_rate = 1
        self._syncro_degree = 0
        self._ramp_vpeak_after = 0
        self._syncro_mode = "AUTO"
        self._trigger = "AUTO"
        self._coupling_change_after = 0
        self._change_polarity_after = "NONE"
        self.device_connection = device_connection
     
    def query(self, command: str) -> Optional[str]:
        """
        Delegates the query command to the device_connection's query method.
        """
        return self.device_connection.query(command)

    def Write(self, command: str) -> None:
        """
        Delegates the write command to the device_connection's write method.
        """
        self.device_connection.write(command)
        
    @property
    def generator_coupling_path(self):
        try:
            resp = self.query("TEST:CWG:COUPLING:VAL?")
            if resp is None:
                raise ValueError("No response received for the generator coupling query.")
            return resp
        except Exception as e:
            print(f"Error retrieving the generator's coupling: {e}")
            return None

    @generator_coupling_path.setter
    def generator_coupling_path(self, coupling: str):
        coupling_config = {"IMP_OUT": 0, "L1_PE": 1, "L1_N": 2, "N_PE": 3}
        try:
            if coupling not in coupling_config:
                raise ValueError(f"Invalid coupling configuration: {coupling}. Must be one of the following {list(self.Coupling_Config.keys())}")
            self.Write(f"TEST:CWG:COUPLING:VAL {coupling}")
        except Exception as e:
            print(f"Error setting the generator's coupling: {e}")

    @property
    def coupling_device(self) -> Optional[str]:
        """
        Gets the current coupling device setting.
        """
        resp = self.query("TEST:CWG:COUPLING_DEVICE:VAL?")
        return resp

    @coupling_device.setter
    def coupling_device(self, value: str) -> None:
        """
        Sets the coupling device.
        """
        if value not in ["INTERN", "EXTERN"]:
            raise ValueError("Invalid coupling_device value")
        self.Write(f"TEST:CWG:COUPLING_DEVICE:VAL {value}")

    @property
    def impedance(self) -> Optional[str]:
        """
        Gets the current impedance setting.
        """
        try:
            resp = self.query("TEST:CWG:IMPEDANCE:VAL?")
            if resp is None:
                raise ValueError("No response received for the impedance query.")
            return resp
        except Exception as e:
            print(f"Error retrieving impedance: {e}")
            return None
        
    @impedance.setter
    def impedance(self, impedance_val: int) -> None:
        """
        Sets the impedance.
        """
        try:
            if isinstance(impedance_val, str) and impedance_val.upper() == "AUTO":
                self.Write(f"TEST:CWG:IMPEDANCE:VAL {impedance_val}")
            elif isinstance(impedance_val, int):
                self.Write(f"TEST:CWG:IMPEDANCE:VAL {impedance_val}")
            else:
                raise ValueError("Invalid input for impedance. Must be 'AUTO'.")
        except Exception as e:
            print(f"Error setting impedance: {e}")

    @property
    def peak_voltage(self) -> Optional[int]:
        """
        Gets the current peak voltage setting.
        """
        try:
            resp = self.query("TEST:CWG:LEVEL:VAL?")
            if resp is None:
                raise ValueError("No response received for the peak voltage query. [Range between 250 and 8000]")
            match = re.search(r"(?<=VAL\s)\d+", resp)
            if match:
                return int(match.group(0))
            else:
                raise ValueError("Invalid response format for the peak voltage query.")
        except Exception as e:
            print(f"Error retrieving peak voltage: {e}")
            return None

    @peak_voltage.setter
    def peak_voltage(self, voltage: int) -> None:
        """
        Sets the peak voltage.
        """
        try:
            self._write(f"TEST:CWG:LEVEL:VAL {voltage}")
        except Exception as e:
            print(f"Error setting peak voltage: {e}")

    @property
    def polarity(self) -> Optional[str]:
        """
        Gets the current voltage polarity setting.
        """
        try:
            resp = self.query("TEST:CWG:POLARITY:VAL?")
            if resp is None:
                raise ValueError("No response received for the polarity query.")
            return resp
        except Exception as e:
            print(f"Error retrieving polarity: {e}")
            return None

    @polarity.setter
    def polarity(self, v_polarity: int) -> None:
        """
        Sets the voltage polarity.
        """
        try: 
            if v_polarity not in ["POS", "NEG"]:
                raise ValueError("Invalid polarity value. Must be 'POS' or 'NEG'.")
            self._write(f"TEST:CWG:POLARITY:VAL {v_polarity}")
        except Exception as e:
            print(f"Error setting polarity: {e}")
            
    @property
    def number_of_pulses(self) -> Optional[int]:
        """
        Gets the current number of pulses setting.
        """
        resp = self.Query("TEST:CWG:PULSE_NB:VAL?")
        return resp
    
    @number_of_pulses.setter
    def number_of_pulses(self, pulse_amount: int) -> None:
        """
        Sets the number of pulses.
        """
        self.Write(f"TEST:CWG:PULSE_NB:VAL {pulse_amount}")

    @property
    def pulse_spacing(self) -> Optional[int]:
        """
        Gets the current repetition rate setting.
        """
        return self.query("TEST:CWG:REPETITION:VAL?")

    @pulse_spacing.setter
    def pulse_spacing(self, rep_rate: int) -> None:
        """
        Sets the repetition rate.
        """
        self.Write(f"TEST:CWG:REPETITION:VAL {rep_rate}")

    @property
    def syncro_degree(self) -> Optional[int]:
        return self.query("TEST:CWG:SYNCRO:DEGREE:VAL?")

    @syncro_degree.setter
    def syncro_degree(self, syncro_degree: int) -> None:
        self.Write(f"TEST:CWG:SYNCRO:DEGREE:VAL {syncro_degree}")

    @property
    def ramp_vpeak_after(self) -> Optional[int]:
        return self.query("TEST:CWG:RAMP:VPEAK:AFTER:VAL?")

    @ramp_vpeak_after.setter
    def ramp_vpeak_after(self, ramp_vpeak_after: int) -> None:
        self.Write(f"TEST:CWG:RAMP:VPEAK:AFTER:VAL {ramp_vpeak_after}")

    @property
    def syncro_mode(self) -> Optional[str]:
        return self.query("TEST:CWG:SYNCRO:MODE:VAL?")

    @syncro_mode.setter
    def syncro_mode(self, syncro_mode: int) -> None:
        self.Write(f"TEST:CWG:SYNCRO:MODE:VAL {syncro_mode}")

    @property
    def trigger(self) -> Optional[str]:
        return self.query("TEST:CWG:TRIGGER:VAL?")

    @trigger.setter
    def trigger(self, trigger: int) -> None:
        self.Write(f"TEST:CWG:TRIGGER:VAL {trigger}")

    @property
    def coupling_change_after(self) -> Optional[int]:
        """
        Gets the current alternating polarity setting.
        """
        return self.query("TEST:CWG:MULTI_COUPLING:CHANGE_AFTER:VAL?")

    @coupling_change_after.setter
    def coupling_change_after(self, coupling_change: int) -> None:
        """
        Sets the alternating polarity.
        """
        self.Write(f"TEST:CWG:MULTI_COUPLING:CHANGE_AFTER:VAL {coupling_change}")

    @property
    def change_polarity_after(self):
        resp = self.query("TEST:CWG:RAMP:POLARITY:AFTER:VAL?")
        return resp
    
    @change_polarity_after.setter
    def change_polarity_after(self, polarity_alteration: str):
        self.Write(f"TEST:CWG:RAMP:POLARITY:AFTER:VAL {polarity_alteration}")

    @property
    def coupling_device_frequency(self):
        """
        Gets the current coupling device frequency setting.
        """
        resp = self.Query("TEST:CWG:COUPLING_DEVICE_FREQUENCY:VAL?")
        return resp

    @coupling_device_frequency.setter
    def coupling_device_frequency(self, value):
        """
        Sets the coupling device frequency.
        """
        if value not in ["CDN_FREQUENCY_AC", "CDN_FREQUENCY_DC"]:
            raise ValueError("Invalid coupling_device_frequency value")
        self.Write(f"TEST:CWG:COUPLING_DEVICE_FREQUENCY:VAL {value}")
    
    
    # region RUN Commands
    @property
    def state(self) -> str:
        """
        Gets the current state of the test.
        """
        return ":RUN:STATE STOPPED\n"

    @property
    def start_test(self) -> str:
        """
        Starts the currently active test.
        """
        return "RUN:START\n"

    @property
    def stop_test(self) -> str:
        """
        Stops the currently running test.
        """
        return "RUN:STOP\n"

    @property
    def mark(self) -> str:
        """
        Adds a marking to the running test.
        """
        return "RUN:MARK \"My Marking\"\n"

    @property
    def fail_mark(self) -> str:
        """
        Adds an EUT-failed mark to the running test.
        """
        return "RUN:FAIL \"Current limit reached\"\n"

    @property
    def eut_failed(self) -> str:
        """
        Checks if the EUT was marked as FAIL.
        """
        return ":RUN:EUT_FAILED NO\n"

    @property
    def last_error(self) -> str:
        """
        Gets the last generator error.
        """
        return ":RUN:ERROR \"No error\"\n"

    @property
    def last_warning(self) -> str:
        """
        Gets the last generator warning.
        """
        return ":RUN:WARNING \"No warning\"\n"

    @property
    def last_event(self) -> str:
        """
        Gets the last occurred generator event.
        """
        return ":RUN:EVENT \"No event\"\n"

    @property
    def trigger(self) -> str:
        """
        Initiates a trigger.
        """
        return "RUN:TRIGGER:TRIGGER\n"

    @property
    def trigger_ready(self) -> str:
        """
        Checks if the system is ready to receive a trigger.
        """
        return ":RUN:TRIGGER:READY NO\n"

    @property
    def manual_trigger(self) -> str:
        """
        Checks if the system must be triggered manually.
        """
        return ":RUN:TRIGGER:MANUAL NO\n"

    @property
    def cycle_time(self) -> str:
        """
        Gets the time of a cycle.
        """
        return ":RUN:CYCLE:TIME 1000\n"

    @property
    def cycle_started(self) -> str:
        """
        Sent from the generator when a cycle has started.
        """
        return "RUN:CYCLE:STARTED\n"

    @property
    def cycle_finished(self) -> str:
        """
        Sent from the generator when a cycle has finished.
        """
        return "RUN:CYCLE:FINISHED\n"

    @property
    def cycle_running(self) -> str:
        """
        Checks if the generator is currently in a cycle.
        """
        return ":RUN:CYCLE:RUNNING NO\n"

    @property
    def total_time(self) -> str:
        """
        Contains the total test time.
        """
        return ":RUN:TIME:TOTAL 1000\n"

    @property
    def current_time(self) -> str:
        """
        Contains the elapsed test time.
        """
        return ":RUN:TIME:CURRENT 100\n"

    @property
    def time_unit(self) -> str:
        """
        Contains the unit of the test time.
        """
        return ":RUN:TIME:UNIT \"s\"\n"

    @property
    def tevent(self) -> str:
        """
        Generator events that can be used for remote control.
        """
        return ":RUN:TEVENT \"No event\"\n"
    # endregion

    # region PUSH Commands
    @property
    def push_enabled(self) -> str:
        """
        Enables push notifications.
        """
        return "PUSH ENABLED\n"

    @property
    def push_disabled(self) -> str:
        """
        Disables push notifications.
        """
        return "PUSH DISABLED\n"

    @property
    def push_state(self) -> str:
        """
        Gets the current state of push notifications.
        """
        return ":PUSH DISABLED\n"
    # endregion

    # region SYSTEM Commands
    @property
    def system_version(self) -> str:
        """
        Returns the version of the installed EPOS.
        """
        return ":SYSTEM:INFO:VERSION \"1.2.1\"\n"

    @property
    def hardware_type(self) -> str:
        """
        Returns the hardware type of the generator.
        """
        return ":SYSTEM:INFO:HARDWARE_TYPE CUPID_V2\n"

    @property
    def serial_number(self) -> str:
        """
        Returns the serial number of the generator.
        """
        return ":SYSTEM:INFO:SERIAL 1240\n"

    @property
    def generator_name(self) -> str:
        """
        Returns the generator type.
        """
        return ":SYSTEM:INFO:NAME IMU3000\n"
    # endregion

    # region ENVIRONMENT Commands
    @property
    def temperature(self) -> str:
        """
        Returns the temperature.
        """
        return ":ENVIRONMENT:TEMPERATURE 20.5\n"

    @property
    def humidity(self) -> str:
        """
        Returns the humidity.
        """
        return ":ENVIRONMENT:HUMIDITY 30\n"

    @property
    def temperature_unit(self) -> str:
        """
        Returns the unit of temperature.
        """
        return ":ENVIRONMENT:UNIT_TEMP C\n"

    @temperature_unit.setter
    def temperature_unit(self, unit: str) -> None:
        """
        Sets the unit of temperature.
        """
        self._temperature_unit = unit
    # endregion

    # region AUDIO Commands
    @property
    def mute_state(self) -> str:
        """
        Gets the mute state.
        """
        return ":AUDIO:MUTE OFF\n"

    @mute_state.setter
    def mute_state(self, state: str) -> None:
        """
        Sets the mute state.
        """
        self._mute_state = state

    @property
    def volume(self) -> str:
        """
        Gets the audio volume.
        """
        return ":AUDIO:VOLUME 0.5\n"

    @volume.setter
    def volume(self, volume: float) -> None:
        """
        Sets the audio volume.
        """
        self._volume = volume

    @property
    def touch_sound_state(self) -> str:
        """
        Gets the touchscreen sound state.
        """
        return ":AUDIO:SOUNDS:TOUCH OFF\n"

    @touch_sound_state.setter
    def touch_sound_state(self, state: str) -> None:
        """
        Sets the touchscreen sound state.
        """
        self._touch_sound_state = state

    @property
    def ready_trigger_sound(self) -> str:
        """
        Gets the sound for a "ready for trigger" event.
        """
        return ":AUDIO:SOUNDS:READY_TRIGGER NO\n"

    @ready_trigger_sound.setter
    def ready_trigger_sound(self, sound: str) -> None:
        """
        Sets the sound for a "ready for trigger" event.
        """
        self._ready_trigger_sound = sound

    @property
    def trigger_sound(self) -> str:
        """
        Gets the sound for a "trigger" event.
        """
        return ":AUDIO:SOUNDS:TRIGGER NO\n"

    @trigger_sound.setter
    def trigger_sound(self, sound: str) -> None:
        """
        Sets the sound for a "trigger" event.
        """
        self._trigger_sound = sound

    @property
    def play_sound(self) -> str:
        """
        Plays a sound.
        """
        return "AUDIO:PLAY BEEP\n"
    # endregion

    # region PROTOCOL Commands
    @property
    def company_name(self) -> str:
        """
        Gets the company name.
        """
        return ":PROTOCOL:GENERAL:COMPANY \"EMC Partner\"\n"

    @company_name.setter
    def company_name(self, name: str) -> None:
        """
        Sets the company name.
        """
        self._company_name = name

    @property
    def operator_name(self) -> str:
        """
        Gets the operator name.
        """
        return ":PROTOCOL:GENERAL:OPERATOR \"Operator\"\n"

    @operator_name.setter
    def operator_name(self, name: str) -> None:
        """
        Sets the operator name.
        """
        self._operator_name = name

    @property
    def eut_description(self) -> str:
        """
        Gets the EUT description.
        """
        return ":PROTOCOL:EUT:DESCRIPTION \"My Eut\"\n"

    @eut_description.setter
    def eut_description(self, description: str) -> None:
        """
        Sets the EUT description.
        """
        self._eut_description = description

    @property
    def eut_serial_number(self) -> str:
        """
        Gets the EUT serial number.
        """
        return ":PROTOCOL:EUT:SERIAL_NO \"#2566\"\n"

    @eut_serial_number.setter
    def eut_serial_number(self, serial_number: str) -> None:
        """
        Sets the EUT serial number.
        """
        self._eut_serial_number = serial_number

    @property
    def eut_comments(self) -> str:
        """
        Gets the EUT comments.
        """
        return ":PROTOCOL:EUT:COMMENTS \"My Comment\"\n"

    @eut_comments.setter
    def eut_comments(self, comments: str) -> None:
        """
        Sets the EUT comments.
        """
        self._eut_comments = comments

    @property
    def csv_format_state(self) -> str:
        """
        Gets the CSV format state.
        """
        return ":PROTOCOL:FORMAT:CSV NO\n"

    @csv_format_state.setter
    def csv_format_state(self, state: str) -> None:
        """
        Sets the CSV format state.
        """
        self._csv_format_state = state

    @property
    def html_format_state(self) -> str:
        """
        Gets the HTML format state.
        """
        return ":PROTOCOL:FORMAT:HTML NO\n"

    @html_format_state.setter
    def html_format_state(self, state: str) -> None:
        """
        Sets the HTML format state.
        """
        self._html_format_state = state

    @property
    def protocol_creation_state(self) -> str:
        """
        Gets the protocol creation state.
        """
        return ":PROTOCOL:CREATE NO\n"

    @protocol_creation_state.setter
    def protocol_creation_state(self, state: str) -> None:
        """
        Sets the protocol creation state.
        """
        self._protocol_creation_state = state

    @property
    def save_name(self) -> str:
        """
        Gets the default save name.
        """
        return ":PROTOCOL:SAVE:NAME YYYYMMDD_HHMMSS\n"

    @save_name.setter
    def save_name(self, name: str) -> None:
        """
        Sets the default save name.
        """
        self._save_name = name

    @property
    def save_path(self) -> str:
        """
        Gets the default save path.
        """
        return ":PROTOCOL:SAVE:PATH \"/myfolder\"\n"

    @save_path.setter
    def save_path(self, path: str) -> None:
        """
        Sets the default save path.
        """
        self._save_path = path
    # endregion