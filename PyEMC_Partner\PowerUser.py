"""
PowerUser.py - Advanced User Functionality Module

This module provides advanced functionalities specifically designed for power users
(engineers) who need detailed control over EMC Partner generators. It includes
custom command execution, advanced parameter manipulation, and specialized test routines.

Class focused on power users
"""

import time
import json
from typing import Dict, Any, List, Optional, Tuple
from GeneratorBase import IMU3000_device_connection, Surge_Test
from CWG_Properties import Device_Properties
from TestManager import TestManager
from error_handling import ErrorHandler


class PowerUser:
    """
    Advanced functionality class for power users (engineers)
    Provides detailed control and custom operations for EMC Partner generators
    """
    
    def __init__(self, device_connection: IMU3000_device_connection):
        """
        Initialize PowerUser with device connection
        
        Args:
            device_connection: Device connection object
        """
        self.device_connection = device_connection
        self.error_handler = ErrorHandler()
        self.custom_profiles = {}
        self.command_history = []
        self.macro_commands = {}
        
    def execute_custom_command(self, command: str) -> Optional[str]:
        """
        Execute a custom SCPI command with detailed logging
        
        Args:
            command (str): SCPI command to execute
            
        Returns:
            Optional[str]: Device response or None if error
        """
        try:
            print(f"🔧 Executing custom command: {command}")
            
            # Log command
            self.command_history.append({
                "command": command,
                "timestamp": time.time(),
                "type": "custom"
            })
            
            # Execute command
            if command.strip().endswith('?'):
                # Query command
                response = self.device_connection.query(command)
                print(f"📥 Response: {response}")
                return response
            else:
                # Write command
                success = self.device_connection.write(command)
                if success:
                    print("✅ Command executed successfully")
                    return "OK"
                else:
                    print("❌ Command execution failed")
                    return None
                    
        except Exception as e:
            self.error_handler.handle_error(f"Error executing custom command: {str(e)}")
            return None
    
    def create_custom_profile(self, name: str, description: str, 
                            parameters: Dict[str, Any]) -> bool:
        """
        Create a custom test profile for reuse
        
        Args:
            name (str): Profile name
            description (str): Profile description
            parameters (Dict[str, Any]): Test parameters
            
        Returns:
            bool: True if profile created successfully
        """
        try:
            # Validate parameters
            required_params = ["peak_voltage", "generator_coupling_path", "polarity"]
            for param in required_params:
                if param not in parameters:
                    self.error_handler.handle_error(f"Missing required parameter: {param}")
                    return False
            
            # Store custom profile
            self.custom_profiles[name] = {
                "name": name,
                "description": description,
                "parameters": parameters,
                "created_time": time.time()
            }
            
            print(f"✅ Custom profile '{name}' created successfully")
            self.error_handler.log_info(f"Custom profile created: {name}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Error creating custom profile: {str(e)}")
            return False
    
    def save_custom_profiles(self, filename: str = "custom_profiles.json") -> bool:
        """
        Save custom profiles to file
        
        Args:
            filename (str): File to save profiles to
            
        Returns:
            bool: True if saved successfully
        """
        try:
            with open(filename, 'w') as f:
                json.dump(self.custom_profiles, f, indent=2)
            
            print(f"✅ Custom profiles saved to {filename}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Error saving custom profiles: {str(e)}")
            return False
    
    def load_custom_profiles(self, filename: str = "custom_profiles.json") -> bool:
        """
        Load custom profiles from file
        
        Args:
            filename (str): File to load profiles from
            
        Returns:
            bool: True if loaded successfully
        """
        try:
            with open(filename, 'r') as f:
                self.custom_profiles = json.load(f)
            
            print(f"✅ Custom profiles loaded from {filename}")
            print(f"📋 Loaded {len(self.custom_profiles)} custom profiles")
            return True
            
        except FileNotFoundError:
            print(f"📝 No custom profiles file found: {filename}")
            return False
        except Exception as e:
            self.error_handler.handle_error(f"Error loading custom profiles: {str(e)}")
            return False
    
    def run_advanced_parametric_test(self, base_parameters: Dict[str, Any],
                                   varying_parameters: Dict[str, List[Any]],
                                   test_matrix: bool = False) -> Dict[str, Any]:
        """
        Run advanced parametric test with multiple varying parameters
        
        Args:
            base_parameters (Dict[str, Any]): Base test parameters
            varying_parameters (Dict[str, List[Any]]): Parameters to vary with their values
            test_matrix (bool): If True, test all combinations; if False, test in sequence
            
        Returns:
            Dict[str, Any]: Test results summary
        """
        print("🧪 Starting Advanced Parametric Test")
        print(f"📊 Varying parameters: {list(varying_parameters.keys())}")
        
        results = {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "start_time": time.time()
        }
        
        try:
            if test_matrix:
                # Test all combinations (matrix)
                test_combinations = self._generate_test_matrix(varying_parameters)
            else:
                # Test parameters in sequence
                test_combinations = self._generate_test_sequence(varying_parameters)
            
            results["total_tests"] = len(test_combinations)
            print(f"🔢 Total test combinations: {results['total_tests']}")
            
            for i, param_set in enumerate(test_combinations, 1):
                print(f"\n--- Test {i}/{results['total_tests']} ---")
                
                # Merge base parameters with current parameter set
                current_params = base_parameters.copy()
                current_params.update(param_set)
                
                # Display current test parameters
                print("📋 Test parameters:")
                for param, value in param_set.items():
                    print(f"  • {param}: {value}")
                
                # Create and execute test
                test = Surge_Test(**current_params)
                test_manager = TestManager(self.device_connection)
                
                start_time = time.time()
                success = test_manager.execute_test(test)
                execution_time = time.time() - start_time
                
                # Record results
                test_result = {
                    "test_number": i,
                    "parameters": param_set,
                    "success": success,
                    "execution_time": execution_time
                }
                results["test_details"].append(test_result)
                
                if success:
                    results["successful_tests"] += 1
                    print(f"✅ Test {i} completed successfully")
                else:
                    results["failed_tests"] += 1
                    print(f"❌ Test {i} failed")
                
                # Brief pause between tests
                time.sleep(1)
            
            results["end_time"] = time.time()
            results["total_time"] = results["end_time"] - results["start_time"]
            
            # Print summary
            self._print_parametric_test_summary(results)
            
            return results
            
        except Exception as e:
            self.error_handler.handle_error(f"Error in advanced parametric test: {str(e)}")
            return results
    
    def _generate_test_matrix(self, varying_parameters: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Generate all combinations of varying parameters"""
        import itertools
        
        param_names = list(varying_parameters.keys())
        param_values = list(varying_parameters.values())
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_set = dict(zip(param_names, combination))
            combinations.append(param_set)
        
        return combinations
    
    def _generate_test_sequence(self, varying_parameters: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Generate sequential parameter variations"""
        sequences = []
        
        # Find the maximum length among all parameter lists
        max_length = max(len(values) for values in varying_parameters.values())
        
        for i in range(max_length):
            param_set = {}
            for param_name, values in varying_parameters.items():
                # Use modulo to cycle through values if list is shorter
                param_set[param_name] = values[i % len(values)]
            sequences.append(param_set)
        
        return sequences
    
    def _print_parametric_test_summary(self, results: Dict[str, Any]):
        """Print summary of parametric test results"""
        print("\n" + "="*60)
        print("📊 ADVANCED PARAMETRIC TEST SUMMARY")
        print("="*60)
        print(f"Total Tests:      {results['total_tests']}")
        print(f"Successful:       {results['successful_tests']}")
        print(f"Failed:           {results['failed_tests']}")
        print(f"Success Rate:     {results['successful_tests']/results['total_tests']*100:.1f}%")
        print(f"Total Time:       {results['total_time']:.2f} seconds")
        print(f"Average per Test: {results['total_time']/results['total_tests']:.2f} seconds")
        
        # Show failed tests if any
        if results['failed_tests'] > 0:
            print("\n❌ Failed Tests:")
            for test in results['test_details']:
                if not test['success']:
                    print(f"  Test {test['test_number']}: {test['parameters']}")
        
        print("="*60)
    
    def create_command_macro(self, name: str, commands: List[str], 
                           description: str = "") -> bool:
        """
        Create a macro of multiple commands for repeated execution
        
        Args:
            name (str): Macro name
            commands (List[str]): List of commands to execute
            description (str): Macro description
            
        Returns:
            bool: True if macro created successfully
        """
        try:
            self.macro_commands[name] = {
                "commands": commands,
                "description": description,
                "created_time": time.time()
            }
            
            print(f"✅ Command macro '{name}' created with {len(commands)} commands")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Error creating command macro: {str(e)}")
            return False
    
    def execute_command_macro(self, name: str) -> bool:
        """
        Execute a command macro
        
        Args:
            name (str): Macro name to execute
            
        Returns:
            bool: True if all commands executed successfully
        """
        if name not in self.macro_commands:
            self.error_handler.handle_error(f"Command macro '{name}' not found")
            return False
        
        macro = self.macro_commands[name]
        commands = macro["commands"]
        
        print(f"🔧 Executing macro '{name}' with {len(commands)} commands")
        
        success_count = 0
        for i, command in enumerate(commands, 1):
            print(f"  Command {i}/{len(commands)}: {command}")
            
            if self.execute_custom_command(command):
                success_count += 1
            else:
                print(f"❌ Command {i} failed, stopping macro execution")
                break
            
            # Brief pause between commands
            time.sleep(0.5)
        
        if success_count == len(commands):
            print(f"✅ Macro '{name}' executed successfully")
            return True
        else:
            print(f"❌ Macro '{name}' partially failed ({success_count}/{len(commands)} successful)")
            return False
    
    def analyze_device_performance(self, test_duration: float = 60.0) -> Dict[str, Any]:
        """
        Analyze device performance over time
        
        Args:
            test_duration (float): Duration to monitor in seconds
            
        Returns:
            Dict[str, Any]: Performance analysis results
        """
        print(f"📊 Starting device performance analysis for {test_duration} seconds")
        
        analysis = {
            "start_time": time.time(),
            "duration": test_duration,
            "samples": [],
            "response_times": [],
            "errors": 0
        }
        
        start_time = time.time()
        sample_interval = 5.0  # Sample every 5 seconds
        
        while time.time() - start_time < test_duration:
            try:
                # Measure response time for a simple query
                query_start = time.time()
                response = self.device_connection.query("*IDN?")
                response_time = time.time() - query_start
                
                if response:
                    analysis["response_times"].append(response_time)
                    analysis["samples"].append({
                        "timestamp": time.time(),
                        "response_time": response_time,
                        "response_length": len(response)
                    })
                else:
                    analysis["errors"] += 1
                
                time.sleep(sample_interval)
                
            except Exception as e:
                analysis["errors"] += 1
                self.error_handler.handle_error(f"Error during performance analysis: {str(e)}")
        
        # Calculate statistics
        if analysis["response_times"]:
            analysis["avg_response_time"] = sum(analysis["response_times"]) / len(analysis["response_times"])
            analysis["min_response_time"] = min(analysis["response_times"])
            analysis["max_response_time"] = max(analysis["response_times"])
        
        analysis["end_time"] = time.time()
        analysis["total_samples"] = len(analysis["samples"])
        
        # Print analysis results
        self._print_performance_analysis(analysis)
        
        return analysis
    
    def _print_performance_analysis(self, analysis: Dict[str, Any]):
        """Print device performance analysis results"""
        print("\n" + "="*50)
        print("📊 DEVICE PERFORMANCE ANALYSIS")
        print("="*50)
        print(f"Duration:         {analysis['duration']:.1f} seconds")
        print(f"Total Samples:    {analysis['total_samples']}")
        print(f"Errors:           {analysis['errors']}")
        
        if analysis.get("avg_response_time"):
            print(f"Avg Response:     {analysis['avg_response_time']*1000:.1f} ms")
            print(f"Min Response:     {analysis['min_response_time']*1000:.1f} ms")
            print(f"Max Response:     {analysis['max_response_time']*1000:.1f} ms")
        
        if analysis["total_samples"] > 0:
            error_rate = analysis["errors"] / (analysis["total_samples"] + analysis["errors"]) * 100
            print(f"Error Rate:       {error_rate:.1f}%")
        
        print("="*50)
    
    def get_command_history(self) -> List[Dict[str, Any]]:
        """
        Get command execution history
        
        Returns:
            List[Dict[str, Any]]: Command history
        """
        return self.command_history
    
    def clear_command_history(self):
        """Clear command execution history"""
        self.command_history.clear()
        print("✅ Command history cleared")
    
    def export_test_data(self, filename: str, data: Dict[str, Any]) -> bool:
        """
        Export test data to JSON file
        
        Args:
            filename (str): Output filename
            data (Dict[str, Any]): Data to export
            
        Returns:
            bool: True if exported successfully
        """
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            print(f"✅ Test data exported to {filename}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Error exporting test data: {str(e)}")
            return False
