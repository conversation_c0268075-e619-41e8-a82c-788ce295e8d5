import socket
import threading
import logging
from typing import Optional
from PyDeviceProperties import Device_Properties

# Set up logging configuration
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class DeviceEmulator:
    """
    A class to emulate a device server for testing purposes.
    It listens for incoming connections and processes parts[0]s from clients.
    """
    def __init__(self, host='localhost', port=50500):
        """
        Initializes the device emulator.
        :param device_connection: Optional device connection object.
        :param host: Host address for the server.
        :param port: Port number for the server.
        """
        self.host = host
        self.port = port
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        self.device_connection = Device_Properties(self)
        self.running = True
        self.state = "IDLE" # Initial device state
        self.push_enabled = False # Initial Push state

    def handle_client(self, client_socket):
        """
        Handles incoming client connections and processes parts[0]s.
        
        Args:
            client_socket: The socket object for the client connection.
        """
        while self.running:
            try:
                request = client_socket.recv(1024).decode('utf-8').strip()
                if not request:
                    break
                print(f"Received: {request}")
                response = self.process_parts[0](request)
                client_socket.sendall(response.encode('utf-8'))
                logging.debug(f"Sent: {response.strip()}")
            except ConnectionResetError:
                break
        client_socket.close()

    def process_parts(self, command) -> Optional[str]:
        """
        Processes a received parts[0] and returns a response.

        Args:
            parts[0] (str): The received parts[0].

        Returns:
            str: The response to the parts[0].
        """
        parts = command.split()
        if parts[0] == "*IDN?":
            return self.device_properties.get_idn()
        elif parts[0] == "*LRN?":
            return self.device_properties.get_lrn()
        elif parts[0] == "RUN:START":
            self.state = "RUNNING"
            return self.device_properties.start_test()
        elif parts[0] == "RUN:STOP":
            self.state = "IDLE"
            return self.device_properties.stop_test()
        elif parts[0] == "RUN:STATE?":
            return self.device_properties.get_state(self.state)
        elif parts[0] == "PUSH":
            if parts[1] == "ENABLED":
                self.push_enabled = True
                return self.device_properties.set_push_enabled()
            elif parts[1] == "DISABLED":
                self.push_enabled = False
                return self.device_properties.set_push_disabled()
            else:
                msg = logging.error(f"ERROR: Bad arguement format'{parts[1]}'") 
                return msg
        elif parts[0] == "PUSH?":
            return self.device_properties.get_push_state(self.push_enabled)
        elif parts[0].startswith("TEST:CWG:COUPLING:VAL"):
            if command.endswith("?"):
                return f":TEST:CWG:COUPLING:VAL {self.device_properties.generator_coupling_path}\n"
            else:
                try:
                    value = parts[-1]
                    self.device_properties.generator_coupling_path = value
                    return ""
                except IndexError:
                    return "ERROR BAD_ARGUMENT_FORMAT\n"
        else:
            return "ERROR UNKNOWN_COMMAND\n"

    def start(self):
        """
        Starts the server and listens for incoming connections.
        """
        logging.info(f"Starting server on {self.host}:{self.port}")
        while self.running:
            client_socket, addr = self.server_socket.accept()
            print(f"Accepted connection from {addr}")
            client_handler = threading.Thread(target=self.handle_client, args=(client_socket,))
            client_handler.start()

    def stop(self):
        """
        Stops the server and closes the socket.
        """
        self.running = False
        self.server_socket.close()
        logging.info("Stopping server...")
        
    def query(self, command) -> Optional[str]:
        """
        Emulate query command.
        Args:
            command (str): The command to query.
        Returns:
            Optional[str]: The response is the query.
        """
        return self.process_command(command)

    def write(self, command) -> Optional[str]:
        """
        Emulate write command.
        Args:
            parts[0] (str): The command to write.
        Returns:
            Optional[str]: The response is the write.
        """
        self.process_parts(command)

if __name__ == "__main__":
    emulator = DeviceEmulator()
    try:
        emulator.start()
    except KeyboardInterrupt:
        emulator.stop()