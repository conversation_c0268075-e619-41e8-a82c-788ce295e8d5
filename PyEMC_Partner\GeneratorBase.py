"""
GeneratorBase.py - EMC Partner Generator Base Communication Module

This module provides the core functionality for communicating with EMC Partner generators.
It handles device connection, command transmission, response processing, and protocol management.

Functions that directly affect/control the device (device interface)
"""

import socket
import time
import serial.tools.list_ports
from typing import Optional, Dict, Any
from error_handling import ErrorHandler


class Surge_Test:
    """
    Data class to hold surge test parameters
    """
    
    def __init__(
        self,
        coupling_device: Optional[str] = "INTERN",
        level: Optional[int] = None,
        polarity: Optional[str] = None,
        change_polarity_after: Optional[int] = None,
        impedance: Optional[str] = None,
        trigger: Optional[str] = "AUTO",
        repetition: Optional[int] = None,
        number_of_pulses: Optional[int] = None,
        generator_coupling_path: Optional[str] = None,
    ):
        """
        Initialize surge test parameters
        
        Args:
            coupling_device: Coupling device type (INTERN, EXTERN, etc.)
            level: Peak voltage level
            polarity: Pulse polarity (POSITIVE, NEGATIVE, ALTERNATING)
            change_polarity_after: Number of pulses before polarity change
            impedance: Output impedance (50, 75, AUTO, etc.)
            trigger: Trigger mode (AUTO, MANUAL, EXTERNAL)
            repetition: Repetition rate in Hz
            number_of_pulses: Total number of pulses
            generator_coupling_path: Coupling path (IMP_OUT, L1_PE, etc.)
        """
        self.parameters = {
            "coupling_device": coupling_device,
            "peak_voltage": level,
            "start_polarity": polarity,
            "change_polarity_after": change_polarity_after,
            "impedance": impedance,
            "trigger": trigger,
            "pulse_spacing": repetition,
            "pulses": number_of_pulses,
            "generator_coupling_path": generator_coupling_path
        }


class IMU3000_device_connection:
    """
    Main class for communicating with IMU3000 and other EMC Partner generators
    Handles connection, command transmission, and response processing
    """
    
    def __init__(self, ip_address: str, port: int):
        """
        Initialize device connection parameters
        
        Args:
            ip_address (str): IP address of the EMC Partner device
            port (int): Communication port number
        """
        self.ip_address = ip_address
        self.port = port
        self.device_socket = None
        self.is_connected = False
        self.log_communications = True
        self.timeout = 5.0
        
        # Initialize error handler
        self.error_handler = ErrorHandler()
        
        # Device state tracking
        self.device_state = "UNKNOWN"
        self.test_running = False
        
    def connect(self) -> bool:
        """
        Establish connection to the EMC Partner device
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.device_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.device_socket.settimeout(self.timeout)
            self.device_socket.connect((self.ip_address, self.port))
            
            # Verify connection with identification query
            response = self.query("*IDN?")
            if response and "EMC" in response.upper():
                self.is_connected = True
                self.error_handler.log_info(f"Connected to device at {self.ip_address}:{self.port}")
                self.error_handler.log_info(f"Device identification: {response.strip()}")
                return True
            else:
                self.error_handler.handle_error("Device identification failed")
                self.disconnect()
                return False
                
        except socket.timeout:
            self.error_handler.handle_error(f"Connection timeout to {self.ip_address}:{self.port}")
            return False
        except socket.error as e:
            self.error_handler.handle_error(f"Socket error during connection: {str(e)}")
            return False
        except Exception as e:
            self.error_handler.handle_error(f"Unexpected error during connection: {str(e)}")
            return False
    
    def disconnect(self):
        """
        Close connection to the device
        """
        try:
            if self.device_socket:
                self.device_socket.close()
                self.device_socket = None
            self.is_connected = False
            self.error_handler.log_info("Device disconnected")
        except Exception as e:
            self.error_handler.handle_error(f"Error during disconnect: {str(e)}")
    
    def write(self, command: str) -> bool:
        """
        Send a command to the device
        
        Args:
            command (str): Command string to send
            
        Returns:
            bool: True if command sent successfully
        """
        if not self.is_connected or not self.device_socket:
            self.error_handler.handle_error("Device not connected")
            return False
        
        try:
            # Ensure command ends with newline
            if not command.endswith('\n'):
                command += '\n'
            
            self.device_socket.sendall(command.encode('utf-8'))
            
            if self.log_communications:
                self.error_handler.log_info(f"Sent: {command.strip()}")
            
            return True
            
        except socket.error as e:
            self.error_handler.handle_error(f"Socket error during write: {str(e)}")
            self.is_connected = False
            return False
        except Exception as e:
            self.error_handler.handle_error(f"Unexpected error during write: {str(e)}")
            return False
    
    def read(self, buffer_size: int = 1024) -> Optional[str]:
        """
        Read response from the device
        
        Args:
            buffer_size (int): Maximum bytes to read
            
        Returns:
            Optional[str]: Response string or None if error
        """
        if not self.is_connected or not self.device_socket:
            self.error_handler.handle_error("Device not connected")
            return None
        
        try:
            response = self.device_socket.recv(buffer_size).decode('utf-8')
            
            if self.log_communications and response.strip():
                self.error_handler.log_info(f"Received: {response.strip()}")
            
            return response
            
        except socket.timeout:
            self.error_handler.handle_error("Read timeout")
            return None
        except socket.error as e:
            self.error_handler.handle_error(f"Socket error during read: {str(e)}")
            self.is_connected = False
            return None
        except Exception as e:
            self.error_handler.handle_error(f"Unexpected error during read: {str(e)}")
            return None
    
    def query(self, command: str, timeout: Optional[float] = None) -> Optional[str]:
        """
        Send a query command and read the response
        
        Args:
            command (str): Query command to send
            timeout (float, optional): Custom timeout for this query
            
        Returns:
            Optional[str]: Response string or None if error
        """
        if timeout:
            original_timeout = self.device_socket.gettimeout()
            self.device_socket.settimeout(timeout)
        
        try:
            if self.write(command):
                # Small delay to ensure command is processed
                time.sleep(0.1)
                response = self.read()
                return response
            return None
            
        finally:
            if timeout:
                self.device_socket.settimeout(original_timeout)
    
    def learn(self) -> Optional[str]:
        """
        Send LRN? command to retrieve current device settings
        
        Returns:
            Optional[str]: Device settings string or None if error
        """
        return self.query("*LRN?")
    
    def get_device_info(self) -> Optional[str]:
        """
        Get device identification information
        
        Returns:
            Optional[str]: Device identification string
        """
        return self.query("*IDN?")
    
    def start_test(self) -> bool:
        """
        Start test execution on the device
        
        Returns:
            bool: True if command sent successfully
        """
        success = self.write("RUN:START")
        if success:
            self.test_running = True
            self.device_state = "RUNNING"
        return success
    
    def stop_test(self) -> bool:
        """
        Stop test execution on the device
        
        Returns:
            bool: True if command sent successfully
        """
        success = self.write("RUN:STOP")
        if success:
            self.test_running = False
            self.device_state = "IDLE"
        return success
    
    def get_test_state(self) -> Optional[str]:
        """
        Query current test state
        
        Returns:
            Optional[str]: Test state string
        """
        return self.query("RUN:STATE?")
    
    def wait_until_test_complete(self, max_wait_time: float = 300.0, poll_interval: float = 1.0) -> bool:
        """
        Wait until test execution is complete
        
        Args:
            max_wait_time (float): Maximum time to wait in seconds
            poll_interval (float): Polling interval in seconds
            
        Returns:
            bool: True if test completed, False if timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            state = self.get_test_state()
            if state and "IDLE" in state.upper():
                self.test_running = False
                self.device_state = "IDLE"
                return True
            
            time.sleep(poll_interval)
        
        self.error_handler.handle_error(f"Test completion timeout after {max_wait_time} seconds")
        return False
    
    def set_push_notifications(self, enabled: bool) -> bool:
        """
        Enable or disable push notifications
        
        Args:
            enabled (bool): True to enable, False to disable
            
        Returns:
            bool: True if command sent successfully
        """
        command = "PUSH ENABLED" if enabled else "PUSH DISABLED"
        return self.write(command)
    
    def get_push_state(self) -> Optional[str]:
        """
        Query push notification state
        
        Returns:
            Optional[str]: Push state string
        """
        return self.query("PUSH?")
    
    @staticmethod
    def list_serial_devices() -> list:
        """
        List all available serial devices
        
        Returns:
            list: List of serial port device names
        """
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            print(f"Error listing serial devices: {e}")
            return []
    
    def __enter__(self):
        """Context manager entry"""
        if self.connect():
            return self
        else:
            raise ConnectionError(f"Failed to connect to {self.ip_address}:{self.port}")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
    
    def __del__(self):
        """Destructor - ensure cleanup"""
        try:
            self.disconnect()
        except:
            pass  # Ignore errors during cleanup
