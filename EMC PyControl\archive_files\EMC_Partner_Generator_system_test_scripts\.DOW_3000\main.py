import socket
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Any
from dataclasses import dataclass
import serial.tools.list_ports

@dataclass
class NetworkConfig:
    """Network configuration parameters."""
    host: str = "************"
    port: int = 50500
    encoding: str = 'utf-8'
    disconnect_msg: str = "!DISCONNECT"

@dataclass
class TestConfig:
    """Test configuration parameters."""
    polarity_alternation: bool = True
    coupling: str = "L1_N"
    output_type: str = "IMP_OUT"
    test_time: int = 600
    active_test: str = "CWG"

class TestClient:
    """Client for managing test equipment over network."""
    
    def __init__(self, network_config: Optional[NetworkConfig] = None):
        """Initialize the test client.
        
        Args:
            network_config: Network configuration parameters
        """
        self.config = network_config or NetworkConfig()
        self.shutdown_flag = False
        self.client_socket = None
        self._setup_logging()
    
    def _setup_logging(self):
        """Configure logging for the client."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """Establish connection to the server.
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.config.host, self.config.port))
            self.logger.info(f"Connected to {self.config.host}:{self.config.port}")
            return True
        except Exception as e:
            self.logger.error(f"Connection failed: {str(e)}")
            return False
    
    def run_test(self, test_config: Optional[TestConfig] = None) -> bool:
        """Execute a test sequence.
        
        Args:
            test_config: Test configuration parameters
            
        Returns:
            bool: True if test completed successfully
        """
        if not self.client_socket:
            self.logger.error("Not connected to server")
            return False
            
        config = test_config or TestConfig()
        
        try:
            # Configure test parameters
            self._send_command("TEST:CWG:POLARITY:ALTERNATE", int(config.polarity_alternation))
            self._send_command("TEST:CWG:COUPLING:VAL", config.coupling)
            self._send_command("TEST:CWG:OUTPUT", config.output_type)
            
            # Start test sequence
            self._send_command("TEST:CWG:START")
            
            # Configure test monitoring
            self._send_command("TEST:CWG:INFO_EVENTS", "TEST_STARTED, TEST_FINISHED, IMPULSE_TRIGGERED")
            self._send_command("TEST:CWG:INFO_TESTTIME", config.test_time)
            self._send_command("TEST:ACTIVE", config.active_test)
            
            self.logger.info("Test sequence completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Test sequence failed: {str(e)}")
            self._send_command("TEST:CWG:STOP")
            return False
    
    def _send_command(self, command: str, value: Any = None) -> Optional[str]:
        """Send command to server and get response.
        
        Args:
            command: Command string
            value: Optional command parameter
            
        Returns:
            Optional[str]: Server response if any
        """
        try:
            cmd = f"{command} {value}" if value is not None else command
            self.client_socket.send(cmd.encode(self.config.encoding))
            return self.client_socket.recv(1024).decode(self.config.encoding)
        except Exception as e:
            self.logger.error(f"Command failed - {command}: {str(e)}")
            return None
    
    def process_options(self, option: str) -> Any:
        """Process different command options.
        
        Args:
            option: Option identifier
            
        Returns:
            Any: Result of option processing
        """
        if option == "That Option":
            return self._send_command("GimmeGimme", {"param": 2})
        elif option == "The other option":
            return self._send_command("Something", {"different": "value"})
        else:
            self.logger.warning(f"Unknown option: {option}")
            return None
    
    def disconnect(self):
        """Gracefully disconnect from server."""
        if self.client_socket:
            try:
                self._send_command(self.config.disconnect_msg)
                self.client_socket.close()
                self.logger.info("Disconnected from server")
            except Exception as e:
                self.logger.error(f"Error during disconnect: {str(e)}")
            finally:
                self.client_socket = None
    
    @staticmethod
    def list_serial_devices() -> list[str]:
        """List all connected serial devices.
        
        Returns:
            list[str]: List of serial port names
        """
        return [port.device for port in serial.tools.list_ports.comports()]
    
    def __del__(self):
        """Ensure cleanup on object destruction."""
        self.disconnect()

def main():
    """Main entry point for the client application."""
    # Print available serial devices
    client = TestClient()
    print("Connected serial devices:", client.list_serial_devices())
    
    # Example test execution
    if client.connect():
        try:
            test_config = TestConfig(
                polarity_alternation=True,
                coupling="L1_N",
                output_type="IMP_OUT",
                test_time=600
            )
            client.run_test(test_config)
        finally:
            client.disconnect()

if __name__ == "__main__":
    main()