# PyEMC Control Software

A Python-based command-line interface (CLI) for controlling EMC Partner generators, designed to reduce the need for front panel HMI manipulation and streamline electromagnetic compatibility testing workflows.

## 🎯 Project Overview

The PyEMC Control Software provides a comprehensive solution for commanding and controlling various EMC Partner generator models through a CLI interface. This software is specifically designed for use by technicians and engineers at SEL (Schweitzer Engineering Laboratories) to enhance testing efficiency and consistency.

### Supported Generator Models

- [ ] IMU-4000
- [ ] IMU-3000  
- [ ] IMU-MG1
- [ ] DOW-3000-S-F
- [ ] DOW-CG1

## ✨ Key Features

### For Basic Users (Technicians)

- **Predefined Test Profiles**: Ready-to-use test configurations for standard testing scenarios
- **Simple Parameter Querying**: Easy access to current device settings and test parameters
- **Guided User Interface**: Intuitive menu-driven CLI with clear instructions
- **Help System**: Built-in documentation and usage instructions

### For Advanced Users (Engineers)

- **Custom Command Strings**: Direct command execution based on device capabilities
- **Interactive Parameter Modification**: Real-time adjustment of test parameters
- **Parametric Testing**: Automated tests with varying parameters
- **Custom Test Profiles**: Creation and execution of specialized test configurations

### System Capabilities

- **Device Communication**: Robust TCP/IP and serial communication with generators
- **Error Handling**: Comprehensive logging and error reporting system
- **Test Management**: Complete test lifecycle management from setup to execution
- **Protocol Support**: CSV and HTML report generation capabilities

## 🏗️ Architecture

The software follows a modular architecture with clear separation of concerns, organized according to the original intended structure:

```
PYSURGE_SURGE/                  # Project Root
├── .py_surge-env/              # Virtual environment folder
├── .vscode/                    # VSCode configuration folder
├── PyEMC_Partner/              # Main project folder
│   ├── CMI.py                  # Direct user interface to interact with devices
│   ├── CWG_Properties.py       # Device module specific commands for "CWG" test parameters
│   ├── GeneratorBase.py        # Functions that directly affect/control the device
│   ├── TestManager.py          # Class and functions to set tests
│   ├── error_handling.py       # Class focused on error handling
│   ├── PowerUser.py            # Class focused on power users
│   ├── console_ux.py           # Class and functions related to console user experience
│   └── device_emulator.py      # Enhanced device emulator for troubleshooting
├── Resources/                  # Folder for additional resources
│   └── gifs/pictures          # Subfolder for images and gifs
├── Script_Archive/             # Archive for old Python scripts (EMC PyControl/)
├── .gitignore                  # Git ignore file
├── README.md                   # Project readme file
├── requirements.txt            # Project dependencies
└── SDD.md                      # Software design specification
```

### Core Components

- **CMI.py**: Main command interface providing direct user interaction and device control
- **GeneratorBase.py**: Core device communication, connection management, and control functions
- **CWG_Properties.py**: Device-specific property management and parameter mappings for CWG test parameters
- **TestManager.py**: Comprehensive test execution, parameter management, and profile handling
- **console_ux.py**: User experience management with menu-driven interfaces for both basic and advanced users
- **PowerUser.py**: Advanced functionality for engineers including custom commands and parametric testing
- **error_handling.py**: Centralized error handling, logging, and user feedback
- **device_emulator.py**: Enhanced device emulator for troubleshooting without physical hardware

## 🚀 Getting Started

### Prerequisites

- Python 3.8.10 or higher
- Network access to EMC Partner generators
- Required Python packages (see requirements.txt)

### Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd "PyEMC Control Software"
   ```

2. **Set up virtual environment** (recommended):

   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

### Configuration

1. **Update device connection settings** in `PyEMC_Partner/CMI.py`:

   ```python
   DEVICE_IP = "************"  # Your generator's IP address
   PORT = 50500                # Communication port
   ```

2. **Verify network connectivity** to your EMC Partner generator

### Basic Usage

1. **Start the application**:

   ```bash
   cd PyEMC_Partner
   python CMI.py
   ```

2. **Follow the interactive prompts** to:
   - Select user type (Basic/Advanced)
   - Connect to your generator
   - View current device settings
   - Select or modify test parameters
   - Execute tests

### Device Emulator for Troubleshooting

The enhanced device emulator allows testing without physical hardware and provides detailed command translation reporting:

1. **Start the emulator** (in a separate terminal):

   ```bash
   cd PyEMC_Partner
   python device_emulator.py
   ```

2. **Connect your application** to the emulator (localhost:50500)

3. **View detailed command translation** in the emulator console for troubleshooting

## 📋 Usage Examples

### Quick Test Execution

```python
# Import and run the main CMI interface
from PyEMC_Partner.CMI import CMI

# Create and run CMI instance
cmi = CMI("************", 50500)
cmi.run_interactive_session()
```

### Advanced Custom Testing

```python
# For advanced users - direct module usage
from PyEMC_Partner.GeneratorBase import IMU3000_device_connection
from PyEMC_Partner.TestManager import TestManager
from PyEMC_Partner.PowerUser import PowerUser

# Connect to device
device = IMU3000_device_connection("************", 50500)
if device.connect():
    # Create test manager and power user interface
    test_manager = TestManager(device)
    power_user = PowerUser(device)

    # Execute predefined test profile
    test_manager.execute_test_profile("IMP_OUT_1000V")

    # Or create custom parametric test
    base_params = {"generator_coupling_path": "IMP_OUT", "polarity": "POSITIVE"}
    varying_params = {"peak_voltage": [500, 1000, 1500, 2000]}
    power_user.run_advanced_parametric_test(base_params, varying_params)
```

### Device Emulator Example

```python
# Start device emulator for testing without hardware
from PyEMC_Partner.device_emulator import DeviceEmulator

emulator = DeviceEmulator(host='localhost', port=50500)
emulator.start_server()  # Provides detailed command translation reporting
```

## 🔧 Development Phases

### Phase 1 (Current)

- ✅ Basic CLI interface for generator control
- ✅ Device communication via TCP/IP
- ✅ Predefined test profiles
- ✅ Parameter modification capabilities
- ✅ Error handling and logging

### Phase 2 (Planned)

- 🔄 Integration with waveform capture software
- 🔄 Advanced analytics and reporting
- 🔄 Enhanced user interface improvements
- 🔄 Extended device model support

## 🛠️ Technical Details

### Communication Protocol

- **Primary**: TCP/IP socket communication (Port 50500)
- **Backup**: Serial communication support
- **Commands**: SCPI-like command structure for device control

### Error Handling

- Centralized error logging to `device_communication.log`
- User-friendly error messages
- Automatic retry mechanisms for transient errors
- Comprehensive exception handling

### Testing Framework

- Built-in device emulator for development testing
- Parametric test capabilities
- Test result logging and reporting
- Protocol management (CSV/HTML output)

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](EMC%20PyControl/LICENSE) file for details.

**Copyright (c) 2024 David Gonzalez**

## 🤝 Contributing

This software is designed for internal use at SEL. For questions, improvements, or bug reports, please contact the development team.

## 📚 Documentation

- **[Software Design Document](SDD.md)**: Comprehensive technical documentation
- **[EPOS Programmer Manual](EPOS_Programmer_Manual.pdf)**: Device programming reference

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failed**: Verify IP address and network connectivity
2. **Invalid Parameters**: Check device capabilities and parameter ranges
3. **Test Execution Errors**: Review device status and error logs

### Support

For technical support and questions:

- Review the Software Design Document (SDD.md)
- Check error logs in `device_communication.log`
- Verify device network configuration
- Ensure all dependencies are properly installed

---

*This software is designed to enhance EMC testing efficiency and consistency while providing both basic and advanced functionality for different user types.*
