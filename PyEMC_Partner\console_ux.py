"""
console_ux.py - Console User Experience Module

This module manages the console user experience for the PyEMC Partner control system.
It provides menu-driven interfaces, user input handling, and display formatting
for both basic and advanced users.

Class and functions related to the console user experience
"""

import sys
from typing import Optional, Dict, Any, List
from TestManager import TestManager, TestProfile
from GeneratorBase import Surge_Test
from error_handling import ErrorHandler


class ConsoleUX:
    """
    Console User Experience manager for PyEMC Partner system
    Provides menu-driven interface for device control and test execution
    """
    
    def __init__(self, test_manager: TestManager, error_handler: ErrorHandler):
        """
        Initialize console UX with test manager and error handler
        
        Args:
            test_manager (TestManager): Test management instance
            error_handler (ErrorHandler): Error handling instance
        """
        self.test_manager = test_manager
        self.error_handler = error_handler
        self.current_menu = "main"
        self.user_type = "basic"  # "basic" or "advanced"
    
    def welcome_message(self):
        """Display welcome message and device information"""
        print("\n" + "="*70)
        print("    🚀 PyEMC Partner Control System")
        print("    EMC Generator Command & Control Interface")
        print("="*70)
        
        # Display device information
        try:
            device_info = self.test_manager.device_connection.get_device_info()
            if device_info:
                print(f"📡 Connected Device: {device_info.strip()}")
            else:
                print("📡 Device: Connection established")
        except Exception as e:
            self.error_handler.handle_error(f"Could not retrieve device info: {str(e)}")
        
        print(f"🔧 Available Test Profiles: {len(self.test_manager.get_available_profiles())}")
        print("="*70)
        
        # User type selection
        self.select_user_type()
    
    def select_user_type(self):
        """Allow user to select their experience level"""
        print("\nSelect your user type:")
        print("1. Basic User (Technician) - Predefined test profiles")
        print("2. Advanced User (Engineer) - Custom parameters and advanced features")
        
        while True:
            choice = input("\nEnter your choice (1-2): ").strip()
            if choice == "1":
                self.user_type = "basic"
                print("✅ Basic user mode selected")
                break
            elif choice == "2":
                self.user_type = "advanced"
                print("✅ Advanced user mode selected")
                break
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
    
    def main_menu(self):
        """Display and handle main menu"""
        while True:
            try:
                print("\n" + "="*50)
                print("📋 MAIN MENU")
                print("="*50)
                
                if self.user_type == "basic":
                    self.display_basic_menu()
                else:
                    self.display_advanced_menu()
                
                choice = input("\n👉 Enter your choice: ").strip()
                
                if not self.handle_menu_choice(choice):
                    break
                    
            except KeyboardInterrupt:
                print("\n\n⚠️  Operation interrupted by user")
                if self.confirm_exit():
                    break
            except Exception as e:
                self.error_handler.handle_error(f"Error in main menu: {str(e)}")
    
    def display_basic_menu(self):
        """Display menu options for basic users"""
        print("1. 📊 View Device Settings")
        print("2. 🧪 Run Predefined Test Profile")
        print("3. 📋 List Available Test Profiles")
        print("4. 📈 View Test History")
        print("5. ❓ Help")
        print("6. 🔄 Switch to Advanced Mode")
        print("7. 🚪 Exit")
    
    def display_advanced_menu(self):
        """Display menu options for advanced users"""
        print("1. 📊 View Device Settings")
        print("2. 🧪 Run Predefined Test Profile")
        print("3. ⚙️  Create Custom Test")
        print("4. 📋 List Available Test Profiles")
        print("5. 🔧 Interactive Parameter Setting")
        print("6. 📈 Run Parametric Test")
        print("7. 📊 View Test History")
        print("8. 🛠️  Device Control Commands")
        print("9. ❓ Help")
        print("10. 🔄 Switch to Basic Mode")
        print("11. 🚪 Exit")
    
    def handle_menu_choice(self, choice: str) -> bool:
        """
        Handle menu choice selection
        
        Args:
            choice (str): User's menu choice
            
        Returns:
            bool: True to continue, False to exit
        """
        try:
            if self.user_type == "basic":
                return self.handle_basic_menu_choice(choice)
            else:
                return self.handle_advanced_menu_choice(choice)
        except Exception as e:
            self.error_handler.handle_error(f"Error handling menu choice: {str(e)}")
            return True
    
    def handle_basic_menu_choice(self, choice: str) -> bool:
        """Handle basic user menu choices"""
        if choice == "1":
            self.view_device_settings()
        elif choice == "2":
            self.run_predefined_test()
        elif choice == "3":
            self.list_test_profiles()
        elif choice == "4":
            self.view_test_history()
        elif choice == "5":
            self.show_help()
        elif choice == "6":
            self.user_type = "advanced"
            print("✅ Switched to Advanced User mode")
        elif choice == "7":
            return self.confirm_exit()
        else:
            print("❌ Invalid choice. Please try again.")
        
        return True
    
    def handle_advanced_menu_choice(self, choice: str) -> bool:
        """Handle advanced user menu choices"""
        if choice == "1":
            self.view_device_settings()
        elif choice == "2":
            self.run_predefined_test()
        elif choice == "3":
            self.create_custom_test()
        elif choice == "4":
            self.list_test_profiles()
        elif choice == "5":
            self.interactive_parameter_setting()
        elif choice == "6":
            self.run_parametric_test()
        elif choice == "7":
            self.view_test_history()
        elif choice == "8":
            self.device_control_commands()
        elif choice == "9":
            self.show_help()
        elif choice == "10":
            self.user_type = "basic"
            print("✅ Switched to Basic User mode")
        elif choice == "11":
            return self.confirm_exit()
        else:
            print("❌ Invalid choice. Please try again.")
        
        return True
    
    def view_device_settings(self):
        """Display current device settings"""
        print("\n" + "="*50)
        print("📊 CURRENT DEVICE SETTINGS")
        print("="*50)
        
        try:
            # Get device settings using LRN command
            settings = self.test_manager.device_connection.learn()
            if settings:
                print("Raw device settings:")
                print(settings)
                
                # Parse and display in a more readable format
                self.parse_and_display_settings(settings)
            else:
                print("❌ Could not retrieve device settings")
                
        except Exception as e:
            self.error_handler.handle_error(f"Error retrieving device settings: {str(e)}")
        
        input("\nPress Enter to continue...")
    
    def parse_and_display_settings(self, settings: str):
        """Parse and display device settings in readable format"""
        print("\n📋 Parsed Settings:")
        print("-" * 30)
        
        lines = settings.split('\n')
        for line in lines:
            line = line.strip()
            if ':' in line and 'VAL' in line:
                try:
                    # Extract parameter and value
                    parts = line.split()
                    if len(parts) >= 2:
                        param = parts[0].replace(':', '').replace('TEST:CWG:', '')
                        value = parts[-1]
                        param_display = param.replace('_', ' ').replace('VAL', '').strip()
                        print(f"{param_display:<20}: {value}")
                except:
                    continue
    
    def run_predefined_test(self):
        """Run a predefined test profile"""
        print("\n" + "="*50)
        print("🧪 RUN PREDEFINED TEST PROFILE")
        print("="*50)
        
        profiles = self.test_manager.get_available_profiles()
        if not profiles:
            print("❌ No test profiles available")
            return
        
        # Display available profiles
        print("Available test profiles:")
        for i, profile_name in enumerate(profiles, 1):
            profile = self.test_manager.get_profile_info(profile_name)
            print(f"{i}. {profile.name} - {profile.description}")
        
        # Get user selection
        while True:
            try:
                choice = input(f"\nSelect profile (1-{len(profiles)}) or 'q' to quit: ").strip()
                if choice.lower() == 'q':
                    return
                
                index = int(choice) - 1
                if 0 <= index < len(profiles):
                    selected_profile = profiles[index]
                    break
                else:
                    print(f"❌ Invalid choice. Please enter 1-{len(profiles)}")
            except ValueError:
                print("❌ Invalid input. Please enter a number.")
        
        # Execute selected profile
        print(f"\n🚀 Executing profile: {selected_profile}")
        success = self.test_manager.execute_test_profile(selected_profile)
        
        if success:
            print("✅ Test completed successfully!")
        else:
            print("❌ Test failed or was cancelled")
        
        input("\nPress Enter to continue...")
    
    def list_test_profiles(self):
        """List all available test profiles with details"""
        print("\n" + "="*50)
        print("📋 AVAILABLE TEST PROFILES")
        print("="*50)
        
        profiles = self.test_manager.get_available_profiles()
        if not profiles:
            print("❌ No test profiles available")
            return
        
        for profile_name in profiles:
            profile = self.test_manager.get_profile_info(profile_name)
            print(f"\n🔹 {profile.name}")
            print(f"   Description: {profile.description}")
            print(f"   Parameters:")
            for param, value in profile.parameters.items():
                param_display = param.replace('_', ' ').title()
                print(f"     • {param_display}: {value}")
        
        input("\nPress Enter to continue...")
    
    def view_test_history(self):
        """Display test execution history"""
        print("\n" + "="*50)
        print("📈 TEST EXECUTION HISTORY")
        print("="*50)
        
        history = self.test_manager.get_test_history()
        if not history:
            print("📝 No test history available")
            return
        
        for i, entry in enumerate(history[-10:], 1):  # Show last 10 tests
            timestamp = entry.get("timestamp", 0)
            execution_time = entry.get("execution_time", 0)
            success = entry.get("success", False)
            
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"{i}. {status} - Duration: {execution_time:.2f}s")
            
            # Show key parameters
            test = entry.get("test")
            if test and hasattr(test, 'parameters'):
                params = test.parameters
                voltage = params.get("peak_voltage", "N/A")
                coupling = params.get("generator_coupling_path", "N/A")
                print(f"   Voltage: {voltage}V, Coupling: {coupling}")
        
        input("\nPress Enter to continue...")
    
    def create_custom_test(self):
        """Create and execute a custom test (advanced users only)"""
        print("\n" + "="*50)
        print("⚙️  CREATE CUSTOM TEST")
        print("="*50)
        
        # Default parameters
        parameters = {
            "peak_voltage": 1000,
            "generator_coupling_path": "IMP_OUT",
            "polarity": "POSITIVE",
            "number_of_pulses": 5,
            "repetition_rate": 1,
            "impedance": "50",
            "trigger": "AUTO",
            "coupling_device": "INTERN"
        }
        
        # Create test object
        test = self.test_manager.create_custom_test(parameters)
        
        # Interactive parameter setting
        if self.test_manager.set_test_parameters_interactive(test):
            # Execute the test
            print("\n🚀 Executing custom test...")
            success = self.test_manager.execute_test(test)
            
            if success:
                print("✅ Custom test completed successfully!")
            else:
                print("❌ Custom test failed or was cancelled")
        else:
            print("❌ Failed to set test parameters")
        
        input("\nPress Enter to continue...")
    
    def interactive_parameter_setting(self):
        """Interactive parameter setting interface"""
        print("\n" + "="*50)
        print("🔧 INTERACTIVE PARAMETER SETTING")
        print("="*50)
        
        # Create a test with default parameters
        default_params = {
            "peak_voltage": 1000,
            "generator_coupling_path": "IMP_OUT",
            "polarity": "POSITIVE",
            "number_of_pulses": 5,
            "repetition_rate": 1,
            "impedance": "50",
            "trigger": "AUTO"
        }
        
        test = self.test_manager.create_custom_test(default_params)
        
        # Interactive setting
        if self.test_manager.set_test_parameters_interactive(test):
            self.test_manager.display_test_parameters(test)
            
            # Ask if user wants to execute
            execute = input("\nExecute this test? (y/N): ").strip().lower()
            if execute == 'y':
                success = self.test_manager.execute_test(test)
                if success:
                    print("✅ Test completed successfully!")
                else:
                    print("❌ Test failed or was cancelled")
        
        input("\nPress Enter to continue...")
    
    def run_parametric_test(self):
        """Run parametric test with varying parameters"""
        print("\n" + "="*50)
        print("📈 PARAMETRIC TEST")
        print("="*50)
        
        # Base parameters
        base_params = {
            "generator_coupling_path": "IMP_OUT",
            "polarity": "POSITIVE",
            "number_of_pulses": 5,
            "repetition_rate": 1,
            "impedance": "50",
            "trigger": "AUTO"
        }
        
        # Select parameter to vary
        print("Select parameter to vary:")
        print("1. Peak Voltage")
        print("2. Number of Pulses")
        print("3. Repetition Rate")
        
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            param = "peak_voltage"
            values = [500, 1000, 1500, 2000]
            print(f"Will test voltages: {values}")
        elif choice == "2":
            param = "number_of_pulses"
            values = [1, 5, 10, 15]
            print(f"Will test pulse counts: {values}")
        elif choice == "3":
            param = "repetition_rate"
            values = [0.5, 1, 2, 5]
            print(f"Will test repetition rates: {values}")
        else:
            print("❌ Invalid choice")
            return
        
        # Confirm and execute
        confirm = input("\nProceed with parametric test? (y/N): ").strip().lower()
        if confirm == 'y':
            success = self.test_manager.run_parametric_test(base_params, param, values)
            if success:
                print("✅ Parametric test completed successfully!")
            else:
                print("❌ Some tests in the parametric sequence failed")
        
        input("\nPress Enter to continue...")
    
    def device_control_commands(self):
        """Device control commands interface"""
        print("\n" + "="*50)
        print("🛠️  DEVICE CONTROL COMMANDS")
        print("="*50)
        
        while True:
            print("\nDevice Control Options:")
            print("1. Start Test")
            print("2. Stop Test")
            print("3. Get Device State")
            print("4. Send Custom Command")
            print("5. Return to Main Menu")
            
            choice = input("\nEnter choice (1-5): ").strip()
            
            if choice == "1":
                if self.test_manager.device_connection.start_test():
                    print("✅ Test started")
                else:
                    print("❌ Failed to start test")
            elif choice == "2":
                if self.test_manager.device_connection.stop_test():
                    print("✅ Test stopped")
                else:
                    print("❌ Failed to stop test")
            elif choice == "3":
                state = self.test_manager.device_connection.get_test_state()
                print(f"Device state: {state}")
            elif choice == "4":
                command = input("Enter command: ").strip()
                if command:
                    response = self.test_manager.device_connection.query(command)
                    print(f"Response: {response}")
            elif choice == "5":
                break
            else:
                print("❌ Invalid choice")
    
    def show_help(self):
        """Display help information"""
        print("\n" + "="*50)
        print("❓ HELP INFORMATION")
        print("="*50)
        
        if self.user_type == "basic":
            print("""
Basic User Guide:
• View Device Settings: Check current generator configuration
• Run Predefined Test: Execute standard test profiles
• List Test Profiles: See all available test configurations
• Test History: Review previous test executions

Navigation:
• Use number keys to select menu options
• Press Enter to confirm selections
• Use Ctrl+C to interrupt operations
            """)
        else:
            print("""
Advanced User Guide:
• All basic user features plus:
• Create Custom Test: Define your own test parameters
• Interactive Parameter Setting: Modify parameters step-by-step
• Parametric Test: Run tests with varying parameter values
• Device Control: Direct device command interface

Custom Commands:
• Use SCPI-like command syntax
• Example: TEST:CWG:LEVEL:VAL 1000
• Query commands end with '?'
• Be careful with direct commands!
            """)
        
        print("\nFor technical support, check the log file: device_communication.log")
        input("\nPress Enter to continue...")
    
    def confirm_exit(self) -> bool:
        """Confirm exit with user"""
        confirm = input("\n🚪 Are you sure you want to exit? (y/N): ").strip().lower()
        if confirm == 'y':
            print("\n👋 Thank you for using PyEMC Partner Control System!")
            print("🔌 Disconnecting from device...")
            try:
                self.test_manager.device_connection.disconnect()
                print("✅ Disconnected successfully")
            except Exception as e:
                print(f"⚠️  Warning during disconnect: {e}")
            return True
        return False
