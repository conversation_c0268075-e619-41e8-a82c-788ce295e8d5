"""
TestManager.py - Test Management and Execution Module

This module manages test parameters, execution, and profiles for EMC Partner generators.
It provides both predefined test profiles for standard users and custom test configurations
for advanced users.

Class and functions to set tests
"""

import time
from typing import Dict, Any, Optional, List, Tuple
from GeneratorBase import Surge_Test, IMU3000_device_connection
from CWG_Properties import Device_Properties
from error_handling import ErrorHandler


class TestProfile:
    """
    Represents a test profile with specific parameters and execution logic
    """
    
    def __init__(self, name: str, description: str, parameters: Dict[str, Any]):
        """
        Initialize a test profile
        
        Args:
            name (str): Profile name
            description (str): Profile description
            parameters (Dict[str, Any]): Test parameters
        """
        self.name = name
        self.description = description
        self.parameters = parameters
        self.execution_time = None
        self.results = None


class TestManager:
    """
    Manages test execution, parameters, and profiles for EMC Partner generators
    """
    
    def __init__(self, device_connection: IMU3000_device_connection):
        """
        Initialize test manager with device connection
        
        Args:
            device_connection: Device connection object
        """
        self.device_connection = device_connection
        self.error_handler = <PERSON>rror<PERSON>andler()
        self.current_test = None
        self.test_profiles = {}
        self.test_history = []
        
        # Initialize predefined test profiles
        self._initialize_predefined_profiles()
    
    def _initialize_predefined_profiles(self):
        """Initialize predefined test profiles for common test scenarios"""
        
        # Zone A EUT AC Power Supply Test Profiles
        self.test_profiles["ZoneA_EUT_AC_500V"] = TestProfile(
            name="Zone A EUT AC 500V",
            description="Zone A EUT AC Power Supply test at 500V",
            parameters={
                "peak_voltage": 500,
                "generator_coupling_path": "L1_N",
                "polarity": "ALTERNATING",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
        
        self.test_profiles["ZoneA_EUT_AC_1000V"] = TestProfile(
            name="Zone A EUT AC 1000V",
            description="Zone A EUT AC Power Supply test at 1000V",
            parameters={
                "peak_voltage": 1000,
                "generator_coupling_path": "L1_N",
                "polarity": "ALTERNATING",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
        
        # Impedance Output Test Profiles
        self.test_profiles["IMP_OUT_500V"] = TestProfile(
            name="Impedance Output 500V",
            description="Impedance output test at 500V",
            parameters={
                "peak_voltage": 500,
                "generator_coupling_path": "IMP_OUT",
                "polarity": "POSITIVE",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
        
        self.test_profiles["IMP_OUT_1000V"] = TestProfile(
            name="Impedance Output 1000V",
            description="Impedance output test at 1000V",
            parameters={
                "peak_voltage": 1000,
                "generator_coupling_path": "IMP_OUT",
                "polarity": "POSITIVE",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
        
        # High voltage test profiles
        self.test_profiles["IMP_OUT_2000V"] = TestProfile(
            name="Impedance Output 2000V",
            description="High voltage impedance output test at 2000V",
            parameters={
                "peak_voltage": 2000,
                "generator_coupling_path": "IMP_OUT",
                "polarity": "POSITIVE",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
        
        self.test_profiles["IMP_OUT_4000V"] = TestProfile(
            name="Impedance Output 4000V",
            description="Maximum voltage impedance output test at 4000V",
            parameters={
                "peak_voltage": 4000,
                "generator_coupling_path": "IMP_OUT",
                "polarity": "POSITIVE",
                "number_of_pulses": 5,
                "repetition_rate": 1,
                "impedance": "50",
                "trigger": "AUTO"
            }
        )
    
    def get_available_profiles(self) -> List[str]:
        """
        Get list of available test profile names
        
        Returns:
            List[str]: List of profile names
        """
        return list(self.test_profiles.keys())
    
    def get_profile_info(self, profile_name: str) -> Optional[TestProfile]:
        """
        Get information about a specific test profile
        
        Args:
            profile_name (str): Name of the profile
            
        Returns:
            Optional[TestProfile]: Profile object or None if not found
        """
        return self.test_profiles.get(profile_name)
    
    def create_custom_test(self, parameters: Dict[str, Any]) -> Surge_Test:
        """
        Create a custom test with specified parameters
        
        Args:
            parameters (Dict[str, Any]): Test parameters
            
        Returns:
            Surge_Test: Custom test object
        """
        return Surge_Test(
            coupling_device=parameters.get("coupling_device", "INTERN"),
            level=parameters.get("peak_voltage", 1000),
            polarity=parameters.get("polarity", "POSITIVE"),
            change_polarity_after=parameters.get("change_polarity_after", None),
            impedance=parameters.get("impedance", "50"),
            trigger=parameters.get("trigger", "AUTO"),
            repetition=parameters.get("repetition_rate", 1),
            number_of_pulses=parameters.get("number_of_pulses", 5),
            generator_coupling_path=parameters.get("generator_coupling_path", "IMP_OUT")
        )
    
    def set_test_parameters_interactive(self, test: Surge_Test) -> bool:
        """
        Allow user to set test parameters interactively
        
        Args:
            test (Surge_Test): Test object to modify
            
        Returns:
            bool: True if parameters were set successfully
        """
        try:
            print("\n" + "="*50)
            print("Interactive Test Parameter Configuration")
            print("="*50)
            print("Press Enter to keep current value, or enter new value:")
            
            # Get current parameters
            params = test.parameters
            
            # Interactive parameter setting
            for param_name, current_value in params.items():
                if current_value is not None:
                    new_value = input(f"{param_name.replace('_', ' ').title()} [{current_value}]: ").strip()
                    if new_value:
                        # Try to convert to appropriate type
                        if isinstance(current_value, int):
                            try:
                                params[param_name] = int(new_value)
                            except ValueError:
                                self.error_handler.handle_error(f"Invalid integer value for {param_name}: {new_value}")
                                return False
                        else:
                            params[param_name] = new_value
            
            # Update test object
            test.parameters = params
            self.current_test = test
            
            print("\nTest parameters updated successfully!")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Error in interactive parameter setting: {str(e)}")
            return False
    
    def display_test_parameters(self, test: Surge_Test) -> None:
        """
        Display current test parameters in a formatted way
        
        Args:
            test (Surge_Test): Test object to display
        """
        print("\n" + "="*50)
        print("Current Test Parameters")
        print("="*50)
        
        for param_name, value in test.parameters.items():
            if value is not None:
                display_name = param_name.replace('_', ' ').title()
                print(f"{display_name:<25}: {value}")
        
        print("="*50)
    
    def validate_test_parameters(self, test: Surge_Test) -> bool:
        """
        Validate test parameters before execution
        
        Args:
            test (Surge_Test): Test object to validate
            
        Returns:
            bool: True if parameters are valid
        """
        params = test.parameters
        
        # Validate peak voltage
        peak_voltage = params.get("peak_voltage")
        if peak_voltage is not None:
            if not (0 < peak_voltage <= 5000):
                self.error_handler.handle_error(f"Peak voltage {peak_voltage}V is out of range (1-5000V)")
                return False
        
        # Validate number of pulses
        num_pulses = params.get("pulses")
        if num_pulses is not None:
            if not (1 <= num_pulses <= 1000):
                self.error_handler.handle_error(f"Number of pulses {num_pulses} is out of range (1-1000)")
                return False
        
        # Validate repetition rate
        rep_rate = params.get("pulse_spacing")
        if rep_rate is not None:
            if not (0.1 <= rep_rate <= 100):
                self.error_handler.handle_error(f"Repetition rate {rep_rate}Hz is out of range (0.1-100Hz)")
                return False
        
        return True
    
    def execute_test_profile(self, profile_name: str) -> bool:
        """
        Execute a predefined test profile
        
        Args:
            profile_name (str): Name of the profile to execute
            
        Returns:
            bool: True if test executed successfully
        """
        profile = self.test_profiles.get(profile_name)
        if not profile:
            self.error_handler.handle_error(f"Test profile '{profile_name}' not found")
            return False
        
        print(f"\nExecuting test profile: {profile.name}")
        print(f"Description: {profile.description}")
        
        # Create test object from profile parameters
        test = self.create_custom_test(profile.parameters)
        
        return self.execute_test(test)
    
    def execute_test(self, test: Surge_Test) -> bool:
        """
        Execute a test with the specified parameters
        
        Args:
            test (Surge_Test): Test object to execute
            
        Returns:
            bool: True if test executed successfully
        """
        try:
            # Validate parameters
            if not self.validate_test_parameters(test):
                return False
            
            # Display test parameters
            self.display_test_parameters(test)
            
            # Confirm execution
            confirm = input("\nExecute this test? (y/N): ").strip().lower()
            if confirm != 'y':
                print("Test execution cancelled by user")
                return False
            
            # Set parameters on device
            print("\nSetting test parameters on device...")
            if not self._apply_parameters_to_device(test):
                return False
            
            # Start test
            print("Starting test execution...")
            start_time = time.time()
            
            if not self.device_connection.start_test():
                self.error_handler.handle_error("Failed to start test")
                return False
            
            # Wait for test completion
            print("Test running... (Press Ctrl+C to stop)")
            if not self.device_connection.wait_until_test_complete():
                self.error_handler.handle_error("Test did not complete successfully")
                return False
            
            execution_time = time.time() - start_time
            print(f"\nTest completed successfully in {execution_time:.2f} seconds")
            
            # Store test in history
            self.test_history.append({
                "test": test,
                "execution_time": execution_time,
                "timestamp": time.time(),
                "success": True
            })
            
            return True
            
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user")
            self.device_connection.stop_test()
            return False
        except Exception as e:
            self.error_handler.handle_error(f"Error during test execution: {str(e)}")
            return False
    
    def _apply_parameters_to_device(self, test: Surge_Test) -> bool:
        """
        Apply test parameters to the device
        
        Args:
            test (Surge_Test): Test object with parameters
            
        Returns:
            bool: True if parameters applied successfully
        """
        try:
            # Get device properties interface
            device_props = Device_Properties(self.device_connection)
            
            # Apply each parameter
            params = test.parameters
            
            if params.get("peak_voltage") is not None:
                device_props.peak_voltage = params["peak_voltage"]
            
            if params.get("generator_coupling_path") is not None:
                device_props.generator_coupling_path = params["generator_coupling_path"]
            
            if params.get("start_polarity") is not None:
                device_props.polarity = params["start_polarity"]
            
            if params.get("pulses") is not None:
                device_props.number_of_pulses = params["pulses"]
            
            if params.get("pulse_spacing") is not None:
                device_props.repetition_rate = params["pulse_spacing"]
            
            if params.get("impedance") is not None:
                device_props.impedance = params["impedance"]
            
            if params.get("trigger") is not None:
                device_props.trigger = params["trigger"]
            
            if params.get("coupling_device") is not None:
                device_props.coupling_device = params["coupling_device"]
            
            self.error_handler.log_info("Test parameters applied to device successfully")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(f"Failed to apply parameters to device: {str(e)}")
            return False
    
    def get_test_history(self) -> List[Dict[str, Any]]:
        """
        Get test execution history
        
        Returns:
            List[Dict[str, Any]]: List of test history entries
        """
        return self.test_history
    
    def run_parametric_test(self, base_parameters: Dict[str, Any], 
                          varying_parameter: str, values: List[Any]) -> bool:
        """
        Run a parametric test with varying parameter values
        
        Args:
            base_parameters (Dict[str, Any]): Base test parameters
            varying_parameter (str): Parameter to vary
            values (List[Any]): List of values for the varying parameter
            
        Returns:
            bool: True if all tests completed successfully
        """
        print(f"\nStarting parametric test varying {varying_parameter}")
        print(f"Values to test: {values}")
        
        success_count = 0
        
        for i, value in enumerate(values):
            print(f"\n--- Parametric Test {i+1}/{len(values)} ---")
            print(f"Setting {varying_parameter} = {value}")
            
            # Create test with current parameter value
            current_params = base_parameters.copy()
            current_params[varying_parameter] = value
            test = self.create_custom_test(current_params)
            
            # Execute test
            if self.execute_test(test):
                success_count += 1
            else:
                print(f"Test failed for {varying_parameter} = {value}")
        
        print(f"\nParametric test completed: {success_count}/{len(values)} tests successful")
        return success_count == len(values)

    def stop_current_test(self) -> bool:
        """
        Stop the currently running test

        Returns:
            bool: True if test stopped successfully
        """
        try:
            return self.device_connection.stop_test()
        except Exception as e:
            self.error_handler.handle_error(f"Error stopping test: {str(e)}")
            return False
