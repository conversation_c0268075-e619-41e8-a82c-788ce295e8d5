"""
device_emulator.py - EMC Partner Device Emulator

This module provides a device emulator for troubleshooting the PyEMC control software
without requiring a physical EMC Partner device. It reports received commands and
their translations to help users identify communication errors.

Purpose:
- Enable testing and development without physical hardware
- Provide detailed command translation reporting for troubleshooting
- Simulate realistic device responses for various test scenarios
- Log all communication for debugging purposes
"""

import socket
import threading
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from CWG_Properties import Device_Properties


class CommandTranslator:
    """
    Handles command translation and provides detailed reporting
    """
    
    def __init__(self):
        self.command_history = []
        self.parameter_mappings = {
            "LEVEL:VAL": "peak_voltage",
            "POLARITY:VAL": "polarity", 
            "PULSE_NB:VAL": "number_of_pulses",
            "REPETITION:VAL": "repetition_rate",
            "TRIGGER:VAL": "trigger",
            "COUPLING:VAL": "generator_coupling_path",
            "COUPLING:DEVICE": "coupling_device",
            "IMPEDANCE:VAL": "impedance",
            "SYNCRO:DEGREE:VAL": "syncro_degree",
            "SYNCRO:MODE:VAL": "syncro_mode"
        }
    
    def translate_command(self, raw_command: str) -> Dict[str, Any]:
        """
        Translate raw command and provide detailed analysis
        
        Args:
            raw_command (str): The raw command received
            
        Returns:
            Dict containing translation details
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        translation = {
            "timestamp": timestamp,
            "raw_command": raw_command.strip(),
            "command_type": "UNKNOWN",
            "parsed_parts": [],
            "parameter": None,
            "value": None,
            "action": "UNKNOWN",
            "translation_status": "SUCCESS",
            "error_message": None
        }
        
        try:
            parts = raw_command.strip().split()
            translation["parsed_parts"] = parts
            
            if not parts:
                translation["translation_status"] = "ERROR"
                translation["error_message"] = "Empty command received"
                return translation
            
            command = parts[0].upper()
            
            # Identify command type and action
            if command.endswith("?"):
                translation["action"] = "QUERY"
                translation["command_type"] = "QUERY"
            elif command in ["*IDN?", "*LRN?"]:
                translation["action"] = "SYSTEM_QUERY"
                translation["command_type"] = "SYSTEM"
            elif command.startswith("RUN:"):
                translation["command_type"] = "TEST_CONTROL"
                translation["action"] = command.split(":")[1]
            elif command.startswith("TEST:"):
                translation["command_type"] = "PARAMETER_SET"
                translation["action"] = "SET_PARAMETER"
                # Extract parameter and value
                if len(parts) > 1:
                    translation["value"] = parts[-1]
                    # Find parameter mapping
                    for mapping_key, param_name in self.parameter_mappings.items():
                        if mapping_key in raw_command:
                            translation["parameter"] = param_name
                            break
            elif command == "PUSH":
                translation["command_type"] = "PUSH_CONTROL"
                translation["action"] = "SET_PUSH_STATE"
                if len(parts) > 1:
                    translation["value"] = parts[1]
            elif command == "PUSH?":
                translation["command_type"] = "PUSH_CONTROL"
                translation["action"] = "QUERY_PUSH_STATE"
            else:
                translation["translation_status"] = "WARNING"
                translation["error_message"] = f"Unknown command: {command}"
                
        except Exception as e:
            translation["translation_status"] = "ERROR"
            translation["error_message"] = f"Translation error: {str(e)}"
        
        # Store in history
        self.command_history.append(translation)
        
        return translation
    
    def print_translation_report(self, translation: Dict[str, Any]):
        """
        Print detailed translation report to console
        """
        print("\n" + "="*80)
        print(f"COMMAND TRANSLATION REPORT - {translation['timestamp']}")
        print("="*80)
        print(f"Raw Command:      '{translation['raw_command']}'")
        print(f"Command Type:     {translation['command_type']}")
        print(f"Action:           {translation['action']}")
        print(f"Parsed Parts:     {translation['parsed_parts']}")
        
        if translation['parameter']:
            print(f"Parameter:        {translation['parameter']}")
        if translation['value']:
            print(f"Value:            {translation['value']}")
            
        print(f"Status:           {translation['translation_status']}")
        
        if translation['error_message']:
            print(f"Error/Warning:    {translation['error_message']}")
            
        print("="*80)


class DeviceEmulator:
    """
    Enhanced EMC Partner Device Emulator for troubleshooting and development
    """
    
    def __init__(self, host: str = 'localhost', port: int = 50500):
        """
        Initialize the device emulator
        
        Args:
            host (str): Host address for the emulator server
            port (int): Port number for the emulator server
        """
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.clients = []
        
        # Device state simulation
        self.device_state = "IDLE"
        self.push_enabled = False
        self.test_running = False
        
        # Command translation and logging
        self.translator = CommandTranslator()
        
        # Set up logging
        self.setup_logging()
        
        # Initialize device properties for realistic responses
        try:
            self.device_properties = Device_Properties(self)
        except Exception as e:
            print(f"Warning: Could not initialize device properties: {e}")
            self.device_properties = None
        
        print(f"EMC Partner Device Emulator initialized on {host}:{port}")
        print("Purpose: Troubleshooting and development without physical hardware")
        print("Features: Command translation reporting, realistic device simulation")
    
    def setup_logging(self):
        """Set up comprehensive logging for the emulator"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('device_emulator.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('DeviceEmulator')
    
    def start_server(self):
        """Start the emulator server and listen for connections"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            
            print(f"\n🚀 EMC Partner Device Emulator STARTED")
            print(f"📡 Listening on {self.host}:{self.port}")
            print(f"📝 Logging to: device_emulator.log")
            print(f"🔧 Ready for troubleshooting and development")
            print("="*60)
            
            self.logger.info(f"Device emulator started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    print(f"\n🔌 Client connected from {addr}")
                    self.logger.info(f"Client connected from {addr}")
                    
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, addr)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        self.logger.error(f"Socket error: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to start server: {e}")
            print(f"❌ Failed to start emulator: {e}")
    
    def handle_client(self, client_socket: socket.socket, addr: tuple):
        """
        Handle client connections and process commands with detailed reporting
        """
        try:
            while self.running:
                try:
                    # Receive command
                    data = client_socket.recv(1024)
                    if not data:
                        break
                        
                    raw_command = data.decode('utf-8').strip()
                    if not raw_command:
                        continue
                    
                    # Translate and analyze command
                    translation = self.translator.translate_command(raw_command)
                    
                    # Print detailed translation report
                    self.translator.print_translation_report(translation)
                    
                    # Process command and generate response
                    response = self.process_command(raw_command, translation)
                    
                    # Send response
                    if response:
                        client_socket.sendall(response.encode('utf-8'))
                        print(f"📤 Response sent: '{response.strip()}'")
                        self.logger.debug(f"Response sent to {addr}: {response.strip()}")
                    
                except ConnectionResetError:
                    break
                except Exception as e:
                    self.logger.error(f"Error handling client {addr}: {e}")
                    break
                    
        finally:
            client_socket.close()
            print(f"🔌 Client {addr} disconnected")
            self.logger.info(f"Client {addr} disconnected")
    
    def process_command(self, command: str, translation: Dict[str, Any]) -> str:
        """
        Process command and return appropriate response
        
        Args:
            command (str): Raw command string
            translation (Dict): Command translation details
            
        Returns:
            str: Response to send back to client
        """
        try:
            parts = command.strip().split()
            if not parts:
                return "ERROR: EMPTY_COMMAND\n"
            
            cmd = parts[0].upper()
            
            # System identification commands
            if cmd == "*IDN?":
                return "EMC Partner,IMU-3000,SN123456,V1.0.0 (EMULATED)\n"
            elif cmd == "*LRN?":
                return self.device_properties.get_lrn()
            
            # Test control commands
            elif cmd == "RUN:START":
                self.device_state = "RUNNING"
                self.test_running = True
                print(f"🏃 Test started - Device state: {self.device_state}")
                return ""
            elif cmd == "RUN:STOP":
                self.device_state = "IDLE"
                self.test_running = False
                print(f"⏹️  Test stopped - Device state: {self.device_state}")
                return ""
            elif cmd == "RUN:STATE?":
                return f"{self.device_state}\n"
            
            # Push notification commands
            elif cmd == "PUSH":
                if len(parts) > 1:
                    if parts[1].upper() == "ENABLED":
                        self.push_enabled = True
                        print(f"🔔 Push notifications ENABLED")
                        return ""
                    elif parts[1].upper() == "DISABLED":
                        self.push_enabled = False
                        print(f"🔕 Push notifications DISABLED")
                        return ""
                return "ERROR: BAD_ARGUMENT_FORMAT\n"
            elif cmd == "PUSH?":
                state = "ENABLED" if self.push_enabled else "DISABLED"
                return f"PUSH {state}\n"
            
            # Parameter setting commands
            elif cmd.startswith("TEST:"):
                if command.endswith("?"):
                    # Query parameter
                    return self.query_parameter(command)
                else:
                    # Set parameter
                    return self.set_parameter(command, parts)
            
            else:
                print(f"❓ Unknown command received: {cmd}")
                return "ERROR: UNKNOWN_COMMAND\n"
                
        except Exception as e:
            self.logger.error(f"Error processing command '{command}': {e}")
            return f"ERROR: PROCESSING_ERROR - {str(e)}\n"
    
    def query_parameter(self, command: str) -> str:
        """Handle parameter query commands"""
        # Simplified parameter queries - extend as needed
        if "COUPLING:VAL" in command:
            return f":TEST:CWG:COUPLING:VAL {self.device_properties.generator_coupling_path}\n"
        elif "LEVEL:VAL" in command:
            return f":TEST:CWG:LEVEL:VAL {self.device_properties.peak_voltage}\n"
        else:
            return "ERROR: UNKNOWN_PARAMETER\n"
    
    def set_parameter(self, command: str, parts: list) -> str:
        """Handle parameter setting commands"""
        try:
            if len(parts) < 2:
                return "ERROR: MISSING_VALUE\n"
            
            value = parts[-1]
            
            if "COUPLING:VAL" in command:
                self.device_properties.generator_coupling_path = value
                print(f"🔧 Parameter set: coupling_path = {value}")
                return ""
            elif "LEVEL:VAL" in command:
                self.device_properties.peak_voltage = int(value)
                print(f"🔧 Parameter set: peak_voltage = {value}")
                return ""
            else:
                return "ERROR: UNKNOWN_PARAMETER\n"
                
        except ValueError as e:
            return f"ERROR: INVALID_VALUE - {str(e)}\n"
    
    def stop_server(self):
        """Stop the emulator server"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        print("\n🛑 EMC Partner Device Emulator STOPPED")
        self.logger.info("Device emulator stopped")
    
    def query(self, command: str) -> Optional[str]:
        """Emulate query method for compatibility"""
        translation = self.translator.translate_command(command)
        return self.process_command(command, translation)
    
    def write(self, command: str):
        """Emulate write method for compatibility"""
        translation = self.translator.translate_command(command)
        self.process_command(command, translation)


def main():
    """Main function to run the device emulator"""
    print("EMC Partner Device Emulator")
    print("For troubleshooting PyEMC control software without physical hardware")
    print("="*70)
    
    emulator = DeviceEmulator()
    
    try:
        emulator.start_server()
    except KeyboardInterrupt:
        print("\n\n⚠️  Emulator interrupted by user")
    finally:
        emulator.stop_server()


if __name__ == "__main__":
    main()
